function hh(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const l of s.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function lc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var oc={exports:{}},os={},ac={exports:{}},D={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fr=Symbol.for("react.element"),ph=Symbol.for("react.portal"),mh=Symbol.for("react.fragment"),vh=Symbol.for("react.strict_mode"),yh=Symbol.for("react.profiler"),gh=Symbol.for("react.provider"),xh=Symbol.for("react.context"),wh=Symbol.for("react.forward_ref"),Sh=Symbol.for("react.suspense"),Eh=Symbol.for("react.memo"),Ch=Symbol.for("react.lazy"),Ea=Symbol.iterator;function kh(e){return e===null||typeof e!="object"?null:(e=Ea&&e[Ea]||e["@@iterator"],typeof e=="function"?e:null)}var uc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cc=Object.assign,fc={};function Dn(e,t,n){this.props=e,this.context=t,this.refs=fc,this.updater=n||uc}Dn.prototype.isReactComponent={};Dn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Dn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function dc(){}dc.prototype=Dn.prototype;function go(e,t,n){this.props=e,this.context=t,this.refs=fc,this.updater=n||uc}var xo=go.prototype=new dc;xo.constructor=go;cc(xo,Dn.prototype);xo.isPureReactComponent=!0;var Ca=Array.isArray,hc=Object.prototype.hasOwnProperty,wo={current:null},pc={key:!0,ref:!0,__self:!0,__source:!0};function mc(e,t,n){var r,i={},s=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(s=""+t.key),t)hc.call(t,r)&&!pc.hasOwnProperty(r)&&(i[r]=t[r]);var o=arguments.length-2;if(o===1)i.children=n;else if(1<o){for(var a=Array(o),u=0;u<o;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in o=e.defaultProps,o)i[r]===void 0&&(i[r]=o[r]);return{$$typeof:Fr,type:e,key:s,ref:l,props:i,_owner:wo.current}}function Nh(e,t){return{$$typeof:Fr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function So(e){return typeof e=="object"&&e!==null&&e.$$typeof===Fr}function Rh(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ka=/\/+/g;function Ls(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Rh(""+e.key):t.toString(36)}function ai(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case Fr:case ph:l=!0}}if(l)return l=e,i=i(l),e=r===""?"."+Ls(l,0):r,Ca(i)?(n="",e!=null&&(n=e.replace(ka,"$&/")+"/"),ai(i,t,n,"",function(u){return u})):i!=null&&(So(i)&&(i=Nh(i,n+(!i.key||l&&l.key===i.key?"":(""+i.key).replace(ka,"$&/")+"/")+e)),t.push(i)),1;if(l=0,r=r===""?".":r+":",Ca(e))for(var o=0;o<e.length;o++){s=e[o];var a=r+Ls(s,o);l+=ai(s,t,n,a,i)}else if(a=kh(e),typeof a=="function")for(e=a.call(e),o=0;!(s=e.next()).done;)s=s.value,a=r+Ls(s,o++),l+=ai(s,t,n,a,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function qr(e,t,n){if(e==null)return e;var r=[],i=0;return ai(e,r,"","",function(s){return t.call(n,s,i++)}),r}function Ph(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ge={current:null},ui={transition:null},jh={ReactCurrentDispatcher:ge,ReactCurrentBatchConfig:ui,ReactCurrentOwner:wo};function vc(){throw Error("act(...) is not supported in production builds of React.")}D.Children={map:qr,forEach:function(e,t,n){qr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return qr(e,function(){t++}),t},toArray:function(e){return qr(e,function(t){return t})||[]},only:function(e){if(!So(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};D.Component=Dn;D.Fragment=mh;D.Profiler=yh;D.PureComponent=go;D.StrictMode=vh;D.Suspense=Sh;D.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=jh;D.act=vc;D.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=cc({},e.props),i=e.key,s=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,l=wo.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(a in t)hc.call(t,a)&&!pc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&o!==void 0?o[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){o=Array(a);for(var u=0;u<a;u++)o[u]=arguments[u+2];r.children=o}return{$$typeof:Fr,type:e.type,key:i,ref:s,props:r,_owner:l}};D.createContext=function(e){return e={$$typeof:xh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:gh,_context:e},e.Consumer=e};D.createElement=mc;D.createFactory=function(e){var t=mc.bind(null,e);return t.type=e,t};D.createRef=function(){return{current:null}};D.forwardRef=function(e){return{$$typeof:wh,render:e}};D.isValidElement=So;D.lazy=function(e){return{$$typeof:Ch,_payload:{_status:-1,_result:e},_init:Ph}};D.memo=function(e,t){return{$$typeof:Eh,type:e,compare:t===void 0?null:t}};D.startTransition=function(e){var t=ui.transition;ui.transition={};try{e()}finally{ui.transition=t}};D.unstable_act=vc;D.useCallback=function(e,t){return ge.current.useCallback(e,t)};D.useContext=function(e){return ge.current.useContext(e)};D.useDebugValue=function(){};D.useDeferredValue=function(e){return ge.current.useDeferredValue(e)};D.useEffect=function(e,t){return ge.current.useEffect(e,t)};D.useId=function(){return ge.current.useId()};D.useImperativeHandle=function(e,t,n){return ge.current.useImperativeHandle(e,t,n)};D.useInsertionEffect=function(e,t){return ge.current.useInsertionEffect(e,t)};D.useLayoutEffect=function(e,t){return ge.current.useLayoutEffect(e,t)};D.useMemo=function(e,t){return ge.current.useMemo(e,t)};D.useReducer=function(e,t,n){return ge.current.useReducer(e,t,n)};D.useRef=function(e){return ge.current.useRef(e)};D.useState=function(e){return ge.current.useState(e)};D.useSyncExternalStore=function(e,t,n){return ge.current.useSyncExternalStore(e,t,n)};D.useTransition=function(){return ge.current.useTransition()};D.version="18.3.1";ac.exports=D;var j=ac.exports;const he=lc(j),Oh=hh({__proto__:null,default:he},[j]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _h=j,Th=Symbol.for("react.element"),Fh=Symbol.for("react.fragment"),Lh=Object.prototype.hasOwnProperty,Ah=_h.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Dh={key:!0,ref:!0,__self:!0,__source:!0};function yc(e,t,n){var r,i={},s=null,l=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)Lh.call(t,r)&&!Dh.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Th,type:e,key:s,ref:l,props:i,_owner:Ah.current}}os.Fragment=Fh;os.jsx=yc;os.jsxs=yc;oc.exports=os;var c=oc.exports,ul={},gc={exports:{}},De={},xc={exports:{}},wc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(R,T){var L=R.length;R.push(T);e:for(;0<L;){var B=L-1>>>1,Y=R[B];if(0<i(Y,T))R[B]=T,R[L]=Y,L=B;else break e}}function n(R){return R.length===0?null:R[0]}function r(R){if(R.length===0)return null;var T=R[0],L=R.pop();if(L!==T){R[0]=L;e:for(var B=0,Y=R.length,br=Y>>>1;B<br;){var $t=2*(B+1)-1,Fs=R[$t],Bt=$t+1,Hr=R[Bt];if(0>i(Fs,L))Bt<Y&&0>i(Hr,Fs)?(R[B]=Hr,R[Bt]=L,B=Bt):(R[B]=Fs,R[$t]=L,B=$t);else if(Bt<Y&&0>i(Hr,L))R[B]=Hr,R[Bt]=L,B=Bt;else break e}}return T}function i(R,T){var L=R.sortIndex-T.sortIndex;return L!==0?L:R.id-T.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var l=Date,o=l.now();e.unstable_now=function(){return l.now()-o}}var a=[],u=[],f=1,d=null,v=3,g=!1,y=!1,x=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(R){for(var T=n(u);T!==null;){if(T.callback===null)r(u);else if(T.startTime<=R)r(u),T.sortIndex=T.expirationTime,t(a,T);else break;T=n(u)}}function E(R){if(x=!1,p(R),!y)if(n(a)!==null)y=!0,we(C);else{var T=n(u);T!==null&&it(E,T.startTime-R)}}function C(R,T){y=!1,x&&(x=!1,m(_),_=-1),g=!0;var L=v;try{for(p(T),d=n(a);d!==null&&(!(d.expirationTime>T)||R&&!ae());){var B=d.callback;if(typeof B=="function"){d.callback=null,v=d.priorityLevel;var Y=B(d.expirationTime<=T);T=e.unstable_now(),typeof Y=="function"?d.callback=Y:d===n(a)&&r(a),p(T)}else r(a);d=n(a)}if(d!==null)var br=!0;else{var $t=n(u);$t!==null&&it(E,$t.startTime-T),br=!1}return br}finally{d=null,v=L,g=!1}}var N=!1,P=null,_=-1,I=5,F=-1;function ae(){return!(e.unstable_now()-F<I)}function Oe(){if(P!==null){var R=e.unstable_now();F=R;var T=!0;try{T=P(!0,R)}finally{T?_e():(N=!1,P=null)}}else N=!1}var _e;if(typeof h=="function")_e=function(){h(Oe)};else if(typeof MessageChannel<"u"){var be=new MessageChannel,mt=be.port2;be.port1.onmessage=Oe,_e=function(){mt.postMessage(null)}}else _e=function(){S(Oe,0)};function we(R){P=R,N||(N=!0,_e())}function it(R,T){_=S(function(){R(e.unstable_now())},T)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(R){R.callback=null},e.unstable_continueExecution=function(){y||g||(y=!0,we(C))},e.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<R?Math.floor(1e3/R):5},e.unstable_getCurrentPriorityLevel=function(){return v},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(R){switch(v){case 1:case 2:case 3:var T=3;break;default:T=v}var L=v;v=T;try{return R()}finally{v=L}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(R,T){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var L=v;v=R;try{return T()}finally{v=L}},e.unstable_scheduleCallback=function(R,T,L){var B=e.unstable_now();switch(typeof L=="object"&&L!==null?(L=L.delay,L=typeof L=="number"&&0<L?B+L:B):L=B,R){case 1:var Y=-1;break;case 2:Y=250;break;case 5:Y=**********;break;case 4:Y=1e4;break;default:Y=5e3}return Y=L+Y,R={id:f++,callback:T,priorityLevel:R,startTime:L,expirationTime:Y,sortIndex:-1},L>B?(R.sortIndex=L,t(u,R),n(a)===null&&R===n(u)&&(x?(m(_),_=-1):x=!0,it(E,L-B))):(R.sortIndex=Y,t(a,R),y||g||(y=!0,we(C))),R},e.unstable_shouldYield=ae,e.unstable_wrapCallback=function(R){var T=v;return function(){var L=v;v=T;try{return R.apply(this,arguments)}finally{v=L}}}})(wc);xc.exports=wc;var Mh=xc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uh=j,Ae=Mh;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Sc=new Set,dr={};function sn(e,t){jn(e,t),jn(e+"Capture",t)}function jn(e,t){for(dr[e]=t,e=0;e<t.length;e++)Sc.add(t[e])}var ct=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),cl=Object.prototype.hasOwnProperty,Ih=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Na={},Ra={};function zh(e){return cl.call(Ra,e)?!0:cl.call(Na,e)?!1:Ih.test(e)?Ra[e]=!0:(Na[e]=!0,!1)}function $h(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Bh(e,t,n,r){if(t===null||typeof t>"u"||$h(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function xe(e,t,n,r,i,s,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=l}var oe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){oe[e]=new xe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];oe[t]=new xe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){oe[e]=new xe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){oe[e]=new xe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){oe[e]=new xe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){oe[e]=new xe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){oe[e]=new xe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){oe[e]=new xe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){oe[e]=new xe(e,5,!1,e.toLowerCase(),null,!1,!1)});var Eo=/[\-:]([a-z])/g;function Co(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Eo,Co);oe[t]=new xe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Eo,Co);oe[t]=new xe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Eo,Co);oe[t]=new xe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){oe[e]=new xe(e,1,!1,e.toLowerCase(),null,!1,!1)});oe.xlinkHref=new xe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){oe[e]=new xe(e,1,!1,e.toLowerCase(),null,!0,!0)});function ko(e,t,n,r){var i=oe.hasOwnProperty(t)?oe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Bh(t,n,i,r)&&(n=null),r||i===null?zh(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var pt=Uh.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Vr=Symbol.for("react.element"),un=Symbol.for("react.portal"),cn=Symbol.for("react.fragment"),No=Symbol.for("react.strict_mode"),fl=Symbol.for("react.profiler"),Ec=Symbol.for("react.provider"),Cc=Symbol.for("react.context"),Ro=Symbol.for("react.forward_ref"),dl=Symbol.for("react.suspense"),hl=Symbol.for("react.suspense_list"),Po=Symbol.for("react.memo"),yt=Symbol.for("react.lazy"),kc=Symbol.for("react.offscreen"),Pa=Symbol.iterator;function Bn(e){return e===null||typeof e!="object"?null:(e=Pa&&e[Pa]||e["@@iterator"],typeof e=="function"?e:null)}var W=Object.assign,As;function Xn(e){if(As===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);As=t&&t[1]||""}return`
`+As+e}var Ds=!1;function Ms(e,t){if(!e||Ds)return"";Ds=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),l=i.length-1,o=s.length-1;1<=l&&0<=o&&i[l]!==s[o];)o--;for(;1<=l&&0<=o;l--,o--)if(i[l]!==s[o]){if(l!==1||o!==1)do if(l--,o--,0>o||i[l]!==s[o]){var a=`
`+i[l].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=l&&0<=o);break}}}finally{Ds=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Xn(e):""}function Qh(e){switch(e.tag){case 5:return Xn(e.type);case 16:return Xn("Lazy");case 13:return Xn("Suspense");case 19:return Xn("SuspenseList");case 0:case 2:case 15:return e=Ms(e.type,!1),e;case 11:return e=Ms(e.type.render,!1),e;case 1:return e=Ms(e.type,!0),e;default:return""}}function pl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case cn:return"Fragment";case un:return"Portal";case fl:return"Profiler";case No:return"StrictMode";case dl:return"Suspense";case hl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Cc:return(e.displayName||"Context")+".Consumer";case Ec:return(e._context.displayName||"Context")+".Provider";case Ro:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Po:return t=e.displayName||null,t!==null?t:pl(e.type)||"Memo";case yt:t=e._payload,e=e._init;try{return pl(e(t))}catch{}}return null}function bh(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return pl(t);case 8:return t===No?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function At(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Nc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Hh(e){var t=Nc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(l){r=""+l,s.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Wr(e){e._valueTracker||(e._valueTracker=Hh(e))}function Rc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Nc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Pi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ml(e,t){var n=t.checked;return W({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ja(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=At(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Pc(e,t){t=t.checked,t!=null&&ko(e,"checked",t,!1)}function vl(e,t){Pc(e,t);var n=At(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?yl(e,t.type,n):t.hasOwnProperty("defaultValue")&&yl(e,t.type,At(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Oa(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function yl(e,t,n){(t!=="number"||Pi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Gn=Array.isArray;function Sn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+At(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function gl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return W({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function _a(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(Gn(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:At(n)}}function jc(e,t){var n=At(t.value),r=At(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ta(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Oc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function xl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Oc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Kr,_c=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Kr=Kr||document.createElement("div"),Kr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Kr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function hr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var tr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},qh=["Webkit","ms","Moz","O"];Object.keys(tr).forEach(function(e){qh.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),tr[t]=tr[e]})});function Tc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||tr.hasOwnProperty(e)&&tr[e]?(""+t).trim():t+"px"}function Fc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Tc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Vh=W({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function wl(e,t){if(t){if(Vh[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function Sl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var El=null;function jo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Cl=null,En=null,Cn=null;function Fa(e){if(e=Dr(e)){if(typeof Cl!="function")throw Error(k(280));var t=e.stateNode;t&&(t=ds(t),Cl(e.stateNode,e.type,t))}}function Lc(e){En?Cn?Cn.push(e):Cn=[e]:En=e}function Ac(){if(En){var e=En,t=Cn;if(Cn=En=null,Fa(e),t)for(e=0;e<t.length;e++)Fa(t[e])}}function Dc(e,t){return e(t)}function Mc(){}var Us=!1;function Uc(e,t,n){if(Us)return e(t,n);Us=!0;try{return Dc(e,t,n)}finally{Us=!1,(En!==null||Cn!==null)&&(Mc(),Ac())}}function pr(e,t){var n=e.stateNode;if(n===null)return null;var r=ds(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var kl=!1;if(ct)try{var Qn={};Object.defineProperty(Qn,"passive",{get:function(){kl=!0}}),window.addEventListener("test",Qn,Qn),window.removeEventListener("test",Qn,Qn)}catch{kl=!1}function Wh(e,t,n,r,i,s,l,o,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var nr=!1,ji=null,Oi=!1,Nl=null,Kh={onError:function(e){nr=!0,ji=e}};function Jh(e,t,n,r,i,s,l,o,a){nr=!1,ji=null,Wh.apply(Kh,arguments)}function Xh(e,t,n,r,i,s,l,o,a){if(Jh.apply(this,arguments),nr){if(nr){var u=ji;nr=!1,ji=null}else throw Error(k(198));Oi||(Oi=!0,Nl=u)}}function ln(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Ic(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function La(e){if(ln(e)!==e)throw Error(k(188))}function Gh(e){var t=e.alternate;if(!t){if(t=ln(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return La(i),e;if(s===r)return La(i),t;s=s.sibling}throw Error(k(188))}if(n.return!==r.return)n=i,r=s;else{for(var l=!1,o=i.child;o;){if(o===n){l=!0,n=i,r=s;break}if(o===r){l=!0,r=i,n=s;break}o=o.sibling}if(!l){for(o=s.child;o;){if(o===n){l=!0,n=s,r=i;break}if(o===r){l=!0,r=s,n=i;break}o=o.sibling}if(!l)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function zc(e){return e=Gh(e),e!==null?$c(e):null}function $c(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=$c(e);if(t!==null)return t;e=e.sibling}return null}var Bc=Ae.unstable_scheduleCallback,Aa=Ae.unstable_cancelCallback,Yh=Ae.unstable_shouldYield,Zh=Ae.unstable_requestPaint,X=Ae.unstable_now,ep=Ae.unstable_getCurrentPriorityLevel,Oo=Ae.unstable_ImmediatePriority,Qc=Ae.unstable_UserBlockingPriority,_i=Ae.unstable_NormalPriority,tp=Ae.unstable_LowPriority,bc=Ae.unstable_IdlePriority,as=null,nt=null;function np(e){if(nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(as,e,void 0,(e.current.flags&128)===128)}catch{}}var Ke=Math.clz32?Math.clz32:sp,rp=Math.log,ip=Math.LN2;function sp(e){return e>>>=0,e===0?32:31-(rp(e)/ip|0)|0}var Jr=64,Xr=4194304;function Yn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ti(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,l=n&268435455;if(l!==0){var o=l&~i;o!==0?r=Yn(o):(s&=l,s!==0&&(r=Yn(s)))}else l=n&~i,l!==0?r=Yn(l):s!==0&&(r=Yn(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ke(t),i=1<<n,r|=e[n],t&=~i;return r}function lp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function op(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var l=31-Ke(s),o=1<<l,a=i[l];a===-1?(!(o&n)||o&r)&&(i[l]=lp(o,t)):a<=t&&(e.expiredLanes|=o),s&=~o}}function Rl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hc(){var e=Jr;return Jr<<=1,!(Jr&4194240)&&(Jr=64),e}function Is(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Lr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ke(t),e[t]=n}function ap(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ke(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function _o(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ke(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var z=0;function qc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Vc,To,Wc,Kc,Jc,Pl=!1,Gr=[],Nt=null,Rt=null,Pt=null,mr=new Map,vr=new Map,wt=[],up="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Da(e,t){switch(e){case"focusin":case"focusout":Nt=null;break;case"dragenter":case"dragleave":Rt=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":mr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":vr.delete(t.pointerId)}}function bn(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=Dr(t),t!==null&&To(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function cp(e,t,n,r,i){switch(t){case"focusin":return Nt=bn(Nt,e,t,n,r,i),!0;case"dragenter":return Rt=bn(Rt,e,t,n,r,i),!0;case"mouseover":return Pt=bn(Pt,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return mr.set(s,bn(mr.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,vr.set(s,bn(vr.get(s)||null,e,t,n,r,i)),!0}return!1}function Xc(e){var t=Ht(e.target);if(t!==null){var n=ln(t);if(n!==null){if(t=n.tag,t===13){if(t=Ic(n),t!==null){e.blockedOn=t,Jc(e.priority,function(){Wc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ci(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=jl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);El=r,n.target.dispatchEvent(r),El=null}else return t=Dr(n),t!==null&&To(t),e.blockedOn=n,!1;t.shift()}return!0}function Ma(e,t,n){ci(e)&&n.delete(t)}function fp(){Pl=!1,Nt!==null&&ci(Nt)&&(Nt=null),Rt!==null&&ci(Rt)&&(Rt=null),Pt!==null&&ci(Pt)&&(Pt=null),mr.forEach(Ma),vr.forEach(Ma)}function Hn(e,t){e.blockedOn===t&&(e.blockedOn=null,Pl||(Pl=!0,Ae.unstable_scheduleCallback(Ae.unstable_NormalPriority,fp)))}function yr(e){function t(i){return Hn(i,e)}if(0<Gr.length){Hn(Gr[0],e);for(var n=1;n<Gr.length;n++){var r=Gr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Nt!==null&&Hn(Nt,e),Rt!==null&&Hn(Rt,e),Pt!==null&&Hn(Pt,e),mr.forEach(t),vr.forEach(t),n=0;n<wt.length;n++)r=wt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<wt.length&&(n=wt[0],n.blockedOn===null);)Xc(n),n.blockedOn===null&&wt.shift()}var kn=pt.ReactCurrentBatchConfig,Fi=!0;function dp(e,t,n,r){var i=z,s=kn.transition;kn.transition=null;try{z=1,Fo(e,t,n,r)}finally{z=i,kn.transition=s}}function hp(e,t,n,r){var i=z,s=kn.transition;kn.transition=null;try{z=4,Fo(e,t,n,r)}finally{z=i,kn.transition=s}}function Fo(e,t,n,r){if(Fi){var i=jl(e,t,n,r);if(i===null)Ks(e,t,r,Li,n),Da(e,r);else if(cp(i,e,t,n,r))r.stopPropagation();else if(Da(e,r),t&4&&-1<up.indexOf(e)){for(;i!==null;){var s=Dr(i);if(s!==null&&Vc(s),s=jl(e,t,n,r),s===null&&Ks(e,t,r,Li,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Ks(e,t,r,null,n)}}var Li=null;function jl(e,t,n,r){if(Li=null,e=jo(r),e=Ht(e),e!==null)if(t=ln(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Ic(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Li=e,null}function Gc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ep()){case Oo:return 1;case Qc:return 4;case _i:case tp:return 16;case bc:return 536870912;default:return 16}default:return 16}}var Et=null,Lo=null,fi=null;function Yc(){if(fi)return fi;var e,t=Lo,n=t.length,r,i="value"in Et?Et.value:Et.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===i[s-r];r++);return fi=i.slice(e,1<r?1-r:void 0)}function di(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Yr(){return!0}function Ua(){return!1}function Me(e){function t(n,r,i,s,l){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=l,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(s):s[o]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Yr:Ua,this.isPropagationStopped=Ua,this}return W(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Yr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Yr)},persist:function(){},isPersistent:Yr}),t}var Mn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ao=Me(Mn),Ar=W({},Mn,{view:0,detail:0}),pp=Me(Ar),zs,$s,qn,us=W({},Ar,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Do,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==qn&&(qn&&e.type==="mousemove"?(zs=e.screenX-qn.screenX,$s=e.screenY-qn.screenY):$s=zs=0,qn=e),zs)},movementY:function(e){return"movementY"in e?e.movementY:$s}}),Ia=Me(us),mp=W({},us,{dataTransfer:0}),vp=Me(mp),yp=W({},Ar,{relatedTarget:0}),Bs=Me(yp),gp=W({},Mn,{animationName:0,elapsedTime:0,pseudoElement:0}),xp=Me(gp),wp=W({},Mn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Sp=Me(wp),Ep=W({},Mn,{data:0}),za=Me(Ep),Cp={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kp={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Np={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Rp(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Np[e])?!!t[e]:!1}function Do(){return Rp}var Pp=W({},Ar,{key:function(e){if(e.key){var t=Cp[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=di(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?kp[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Do,charCode:function(e){return e.type==="keypress"?di(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?di(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),jp=Me(Pp),Op=W({},us,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),$a=Me(Op),_p=W({},Ar,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Do}),Tp=Me(_p),Fp=W({},Mn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Lp=Me(Fp),Ap=W({},us,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Dp=Me(Ap),Mp=[9,13,27,32],Mo=ct&&"CompositionEvent"in window,rr=null;ct&&"documentMode"in document&&(rr=document.documentMode);var Up=ct&&"TextEvent"in window&&!rr,Zc=ct&&(!Mo||rr&&8<rr&&11>=rr),Ba=" ",Qa=!1;function ef(e,t){switch(e){case"keyup":return Mp.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function tf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var fn=!1;function Ip(e,t){switch(e){case"compositionend":return tf(t);case"keypress":return t.which!==32?null:(Qa=!0,Ba);case"textInput":return e=t.data,e===Ba&&Qa?null:e;default:return null}}function zp(e,t){if(fn)return e==="compositionend"||!Mo&&ef(e,t)?(e=Yc(),fi=Lo=Et=null,fn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Zc&&t.locale!=="ko"?null:t.data;default:return null}}var $p={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ba(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!$p[e.type]:t==="textarea"}function nf(e,t,n,r){Lc(r),t=Ai(t,"onChange"),0<t.length&&(n=new Ao("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var ir=null,gr=null;function Bp(e){pf(e,0)}function cs(e){var t=pn(e);if(Rc(t))return e}function Qp(e,t){if(e==="change")return t}var rf=!1;if(ct){var Qs;if(ct){var bs="oninput"in document;if(!bs){var Ha=document.createElement("div");Ha.setAttribute("oninput","return;"),bs=typeof Ha.oninput=="function"}Qs=bs}else Qs=!1;rf=Qs&&(!document.documentMode||9<document.documentMode)}function qa(){ir&&(ir.detachEvent("onpropertychange",sf),gr=ir=null)}function sf(e){if(e.propertyName==="value"&&cs(gr)){var t=[];nf(t,gr,e,jo(e)),Uc(Bp,t)}}function bp(e,t,n){e==="focusin"?(qa(),ir=t,gr=n,ir.attachEvent("onpropertychange",sf)):e==="focusout"&&qa()}function Hp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return cs(gr)}function qp(e,t){if(e==="click")return cs(t)}function Vp(e,t){if(e==="input"||e==="change")return cs(t)}function Wp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Xe=typeof Object.is=="function"?Object.is:Wp;function xr(e,t){if(Xe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!cl.call(t,i)||!Xe(e[i],t[i]))return!1}return!0}function Va(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Wa(e,t){var n=Va(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Va(n)}}function lf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?lf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function of(){for(var e=window,t=Pi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Pi(e.document)}return t}function Uo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Kp(e){var t=of(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&lf(n.ownerDocument.documentElement,n)){if(r!==null&&Uo(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Wa(n,s);var l=Wa(n,r);i&&l&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Jp=ct&&"documentMode"in document&&11>=document.documentMode,dn=null,Ol=null,sr=null,_l=!1;function Ka(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;_l||dn==null||dn!==Pi(r)||(r=dn,"selectionStart"in r&&Uo(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),sr&&xr(sr,r)||(sr=r,r=Ai(Ol,"onSelect"),0<r.length&&(t=new Ao("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=dn)))}function Zr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var hn={animationend:Zr("Animation","AnimationEnd"),animationiteration:Zr("Animation","AnimationIteration"),animationstart:Zr("Animation","AnimationStart"),transitionend:Zr("Transition","TransitionEnd")},Hs={},af={};ct&&(af=document.createElement("div").style,"AnimationEvent"in window||(delete hn.animationend.animation,delete hn.animationiteration.animation,delete hn.animationstart.animation),"TransitionEvent"in window||delete hn.transitionend.transition);function fs(e){if(Hs[e])return Hs[e];if(!hn[e])return e;var t=hn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in af)return Hs[e]=t[n];return e}var uf=fs("animationend"),cf=fs("animationiteration"),ff=fs("animationstart"),df=fs("transitionend"),hf=new Map,Ja="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Mt(e,t){hf.set(e,t),sn(t,[e])}for(var qs=0;qs<Ja.length;qs++){var Vs=Ja[qs],Xp=Vs.toLowerCase(),Gp=Vs[0].toUpperCase()+Vs.slice(1);Mt(Xp,"on"+Gp)}Mt(uf,"onAnimationEnd");Mt(cf,"onAnimationIteration");Mt(ff,"onAnimationStart");Mt("dblclick","onDoubleClick");Mt("focusin","onFocus");Mt("focusout","onBlur");Mt(df,"onTransitionEnd");jn("onMouseEnter",["mouseout","mouseover"]);jn("onMouseLeave",["mouseout","mouseover"]);jn("onPointerEnter",["pointerout","pointerover"]);jn("onPointerLeave",["pointerout","pointerover"]);sn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));sn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));sn("onBeforeInput",["compositionend","keypress","textInput","paste"]);sn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));sn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));sn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Yp=new Set("cancel close invalid load scroll toggle".split(" ").concat(Zn));function Xa(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Xh(r,t,void 0,e),e.currentTarget=null}function pf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var l=r.length-1;0<=l;l--){var o=r[l],a=o.instance,u=o.currentTarget;if(o=o.listener,a!==s&&i.isPropagationStopped())break e;Xa(i,o,u),s=a}else for(l=0;l<r.length;l++){if(o=r[l],a=o.instance,u=o.currentTarget,o=o.listener,a!==s&&i.isPropagationStopped())break e;Xa(i,o,u),s=a}}}if(Oi)throw e=Nl,Oi=!1,Nl=null,e}function Q(e,t){var n=t[Dl];n===void 0&&(n=t[Dl]=new Set);var r=e+"__bubble";n.has(r)||(mf(t,e,2,!1),n.add(r))}function Ws(e,t,n){var r=0;t&&(r|=4),mf(n,e,r,t)}var ei="_reactListening"+Math.random().toString(36).slice(2);function wr(e){if(!e[ei]){e[ei]=!0,Sc.forEach(function(n){n!=="selectionchange"&&(Yp.has(n)||Ws(n,!1,e),Ws(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ei]||(t[ei]=!0,Ws("selectionchange",!1,t))}}function mf(e,t,n,r){switch(Gc(t)){case 1:var i=dp;break;case 4:i=hp;break;default:i=Fo}n=i.bind(null,t,n,e),i=void 0,!kl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Ks(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var o=r.stateNode.containerInfo;if(o===i||o.nodeType===8&&o.parentNode===i)break;if(l===4)for(l=r.return;l!==null;){var a=l.tag;if((a===3||a===4)&&(a=l.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;l=l.return}for(;o!==null;){if(l=Ht(o),l===null)return;if(a=l.tag,a===5||a===6){r=s=l;continue e}o=o.parentNode}}r=r.return}Uc(function(){var u=s,f=jo(n),d=[];e:{var v=hf.get(e);if(v!==void 0){var g=Ao,y=e;switch(e){case"keypress":if(di(n)===0)break e;case"keydown":case"keyup":g=jp;break;case"focusin":y="focus",g=Bs;break;case"focusout":y="blur",g=Bs;break;case"beforeblur":case"afterblur":g=Bs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Ia;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=vp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=Tp;break;case uf:case cf:case ff:g=xp;break;case df:g=Lp;break;case"scroll":g=pp;break;case"wheel":g=Dp;break;case"copy":case"cut":case"paste":g=Sp;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=$a}var x=(t&4)!==0,S=!x&&e==="scroll",m=x?v!==null?v+"Capture":null:v;x=[];for(var h=u,p;h!==null;){p=h;var E=p.stateNode;if(p.tag===5&&E!==null&&(p=E,m!==null&&(E=pr(h,m),E!=null&&x.push(Sr(h,E,p)))),S)break;h=h.return}0<x.length&&(v=new g(v,y,null,n,f),d.push({event:v,listeners:x}))}}if(!(t&7)){e:{if(v=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",v&&n!==El&&(y=n.relatedTarget||n.fromElement)&&(Ht(y)||y[ft]))break e;if((g||v)&&(v=f.window===f?f:(v=f.ownerDocument)?v.defaultView||v.parentWindow:window,g?(y=n.relatedTarget||n.toElement,g=u,y=y?Ht(y):null,y!==null&&(S=ln(y),y!==S||y.tag!==5&&y.tag!==6)&&(y=null)):(g=null,y=u),g!==y)){if(x=Ia,E="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(x=$a,E="onPointerLeave",m="onPointerEnter",h="pointer"),S=g==null?v:pn(g),p=y==null?v:pn(y),v=new x(E,h+"leave",g,n,f),v.target=S,v.relatedTarget=p,E=null,Ht(f)===u&&(x=new x(m,h+"enter",y,n,f),x.target=p,x.relatedTarget=S,E=x),S=E,g&&y)t:{for(x=g,m=y,h=0,p=x;p;p=an(p))h++;for(p=0,E=m;E;E=an(E))p++;for(;0<h-p;)x=an(x),h--;for(;0<p-h;)m=an(m),p--;for(;h--;){if(x===m||m!==null&&x===m.alternate)break t;x=an(x),m=an(m)}x=null}else x=null;g!==null&&Ga(d,v,g,x,!1),y!==null&&S!==null&&Ga(d,S,y,x,!0)}}e:{if(v=u?pn(u):window,g=v.nodeName&&v.nodeName.toLowerCase(),g==="select"||g==="input"&&v.type==="file")var C=Qp;else if(ba(v))if(rf)C=Vp;else{C=Hp;var N=bp}else(g=v.nodeName)&&g.toLowerCase()==="input"&&(v.type==="checkbox"||v.type==="radio")&&(C=qp);if(C&&(C=C(e,u))){nf(d,C,n,f);break e}N&&N(e,v,u),e==="focusout"&&(N=v._wrapperState)&&N.controlled&&v.type==="number"&&yl(v,"number",v.value)}switch(N=u?pn(u):window,e){case"focusin":(ba(N)||N.contentEditable==="true")&&(dn=N,Ol=u,sr=null);break;case"focusout":sr=Ol=dn=null;break;case"mousedown":_l=!0;break;case"contextmenu":case"mouseup":case"dragend":_l=!1,Ka(d,n,f);break;case"selectionchange":if(Jp)break;case"keydown":case"keyup":Ka(d,n,f)}var P;if(Mo)e:{switch(e){case"compositionstart":var _="onCompositionStart";break e;case"compositionend":_="onCompositionEnd";break e;case"compositionupdate":_="onCompositionUpdate";break e}_=void 0}else fn?ef(e,n)&&(_="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(_="onCompositionStart");_&&(Zc&&n.locale!=="ko"&&(fn||_!=="onCompositionStart"?_==="onCompositionEnd"&&fn&&(P=Yc()):(Et=f,Lo="value"in Et?Et.value:Et.textContent,fn=!0)),N=Ai(u,_),0<N.length&&(_=new za(_,e,null,n,f),d.push({event:_,listeners:N}),P?_.data=P:(P=tf(n),P!==null&&(_.data=P)))),(P=Up?Ip(e,n):zp(e,n))&&(u=Ai(u,"onBeforeInput"),0<u.length&&(f=new za("onBeforeInput","beforeinput",null,n,f),d.push({event:f,listeners:u}),f.data=P))}pf(d,t)})}function Sr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ai(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=pr(e,n),s!=null&&r.unshift(Sr(e,s,i)),s=pr(e,t),s!=null&&r.push(Sr(e,s,i))),e=e.return}return r}function an(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Ga(e,t,n,r,i){for(var s=t._reactName,l=[];n!==null&&n!==r;){var o=n,a=o.alternate,u=o.stateNode;if(a!==null&&a===r)break;o.tag===5&&u!==null&&(o=u,i?(a=pr(n,s),a!=null&&l.unshift(Sr(n,a,o))):i||(a=pr(n,s),a!=null&&l.push(Sr(n,a,o)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var Zp=/\r\n?/g,em=/\u0000|\uFFFD/g;function Ya(e){return(typeof e=="string"?e:""+e).replace(Zp,`
`).replace(em,"")}function ti(e,t,n){if(t=Ya(t),Ya(e)!==t&&n)throw Error(k(425))}function Di(){}var Tl=null,Fl=null;function Ll(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Al=typeof setTimeout=="function"?setTimeout:void 0,tm=typeof clearTimeout=="function"?clearTimeout:void 0,Za=typeof Promise=="function"?Promise:void 0,nm=typeof queueMicrotask=="function"?queueMicrotask:typeof Za<"u"?function(e){return Za.resolve(null).then(e).catch(rm)}:Al;function rm(e){setTimeout(function(){throw e})}function Js(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),yr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);yr(t)}function jt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function eu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Un=Math.random().toString(36).slice(2),tt="__reactFiber$"+Un,Er="__reactProps$"+Un,ft="__reactContainer$"+Un,Dl="__reactEvents$"+Un,im="__reactListeners$"+Un,sm="__reactHandles$"+Un;function Ht(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ft]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=eu(e);e!==null;){if(n=e[tt])return n;e=eu(e)}return t}e=n,n=e.parentNode}return null}function Dr(e){return e=e[tt]||e[ft],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function pn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function ds(e){return e[Er]||null}var Ml=[],mn=-1;function Ut(e){return{current:e}}function b(e){0>mn||(e.current=Ml[mn],Ml[mn]=null,mn--)}function $(e,t){mn++,Ml[mn]=e.current,e.current=t}var Dt={},me=Ut(Dt),Ce=Ut(!1),Yt=Dt;function On(e,t){var n=e.type.contextTypes;if(!n)return Dt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function ke(e){return e=e.childContextTypes,e!=null}function Mi(){b(Ce),b(me)}function tu(e,t,n){if(me.current!==Dt)throw Error(k(168));$(me,t),$(Ce,n)}function vf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(k(108,bh(e)||"Unknown",i));return W({},n,r)}function Ui(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Dt,Yt=me.current,$(me,e),$(Ce,Ce.current),!0}function nu(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=vf(e,t,Yt),r.__reactInternalMemoizedMergedChildContext=e,b(Ce),b(me),$(me,e)):b(Ce),$(Ce,n)}var lt=null,hs=!1,Xs=!1;function yf(e){lt===null?lt=[e]:lt.push(e)}function lm(e){hs=!0,yf(e)}function It(){if(!Xs&&lt!==null){Xs=!0;var e=0,t=z;try{var n=lt;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,hs=!1}catch(i){throw lt!==null&&(lt=lt.slice(e+1)),Bc(Oo,It),i}finally{z=t,Xs=!1}}return null}var vn=[],yn=0,Ii=null,zi=0,Ue=[],Ie=0,Zt=null,ot=1,at="";function Qt(e,t){vn[yn++]=zi,vn[yn++]=Ii,Ii=e,zi=t}function gf(e,t,n){Ue[Ie++]=ot,Ue[Ie++]=at,Ue[Ie++]=Zt,Zt=e;var r=ot;e=at;var i=32-Ke(r)-1;r&=~(1<<i),n+=1;var s=32-Ke(t)+i;if(30<s){var l=i-i%5;s=(r&(1<<l)-1).toString(32),r>>=l,i-=l,ot=1<<32-Ke(t)+i|n<<i|r,at=s+e}else ot=1<<s|n<<i|r,at=e}function Io(e){e.return!==null&&(Qt(e,1),gf(e,1,0))}function zo(e){for(;e===Ii;)Ii=vn[--yn],vn[yn]=null,zi=vn[--yn],vn[yn]=null;for(;e===Zt;)Zt=Ue[--Ie],Ue[Ie]=null,at=Ue[--Ie],Ue[Ie]=null,ot=Ue[--Ie],Ue[Ie]=null}var Le=null,Fe=null,H=!1,We=null;function xf(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ru(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Le=e,Fe=jt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Le=e,Fe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Zt!==null?{id:ot,overflow:at}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Le=e,Fe=null,!0):!1;default:return!1}}function Ul(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Il(e){if(H){var t=Fe;if(t){var n=t;if(!ru(e,t)){if(Ul(e))throw Error(k(418));t=jt(n.nextSibling);var r=Le;t&&ru(e,t)?xf(r,n):(e.flags=e.flags&-4097|2,H=!1,Le=e)}}else{if(Ul(e))throw Error(k(418));e.flags=e.flags&-4097|2,H=!1,Le=e}}}function iu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Le=e}function ni(e){if(e!==Le)return!1;if(!H)return iu(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ll(e.type,e.memoizedProps)),t&&(t=Fe)){if(Ul(e))throw wf(),Error(k(418));for(;t;)xf(e,t),t=jt(t.nextSibling)}if(iu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Fe=jt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Fe=null}}else Fe=Le?jt(e.stateNode.nextSibling):null;return!0}function wf(){for(var e=Fe;e;)e=jt(e.nextSibling)}function _n(){Fe=Le=null,H=!1}function $o(e){We===null?We=[e]:We.push(e)}var om=pt.ReactCurrentBatchConfig;function Vn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(l){var o=i.refs;l===null?delete o[s]:o[s]=l},t._stringRef=s,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function ri(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function su(e){var t=e._init;return t(e._payload)}function Sf(e){function t(m,h){if(e){var p=m.deletions;p===null?(m.deletions=[h],m.flags|=16):p.push(h)}}function n(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function r(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function i(m,h){return m=Ft(m,h),m.index=0,m.sibling=null,m}function s(m,h,p){return m.index=p,e?(p=m.alternate,p!==null?(p=p.index,p<h?(m.flags|=2,h):p):(m.flags|=2,h)):(m.flags|=1048576,h)}function l(m){return e&&m.alternate===null&&(m.flags|=2),m}function o(m,h,p,E){return h===null||h.tag!==6?(h=rl(p,m.mode,E),h.return=m,h):(h=i(h,p),h.return=m,h)}function a(m,h,p,E){var C=p.type;return C===cn?f(m,h,p.props.children,E,p.key):h!==null&&(h.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===yt&&su(C)===h.type)?(E=i(h,p.props),E.ref=Vn(m,h,p),E.return=m,E):(E=xi(p.type,p.key,p.props,null,m.mode,E),E.ref=Vn(m,h,p),E.return=m,E)}function u(m,h,p,E){return h===null||h.tag!==4||h.stateNode.containerInfo!==p.containerInfo||h.stateNode.implementation!==p.implementation?(h=il(p,m.mode,E),h.return=m,h):(h=i(h,p.children||[]),h.return=m,h)}function f(m,h,p,E,C){return h===null||h.tag!==7?(h=Xt(p,m.mode,E,C),h.return=m,h):(h=i(h,p),h.return=m,h)}function d(m,h,p){if(typeof h=="string"&&h!==""||typeof h=="number")return h=rl(""+h,m.mode,p),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Vr:return p=xi(h.type,h.key,h.props,null,m.mode,p),p.ref=Vn(m,null,h),p.return=m,p;case un:return h=il(h,m.mode,p),h.return=m,h;case yt:var E=h._init;return d(m,E(h._payload),p)}if(Gn(h)||Bn(h))return h=Xt(h,m.mode,p,null),h.return=m,h;ri(m,h)}return null}function v(m,h,p,E){var C=h!==null?h.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return C!==null?null:o(m,h,""+p,E);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Vr:return p.key===C?a(m,h,p,E):null;case un:return p.key===C?u(m,h,p,E):null;case yt:return C=p._init,v(m,h,C(p._payload),E)}if(Gn(p)||Bn(p))return C!==null?null:f(m,h,p,E,null);ri(m,p)}return null}function g(m,h,p,E,C){if(typeof E=="string"&&E!==""||typeof E=="number")return m=m.get(p)||null,o(h,m,""+E,C);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case Vr:return m=m.get(E.key===null?p:E.key)||null,a(h,m,E,C);case un:return m=m.get(E.key===null?p:E.key)||null,u(h,m,E,C);case yt:var N=E._init;return g(m,h,p,N(E._payload),C)}if(Gn(E)||Bn(E))return m=m.get(p)||null,f(h,m,E,C,null);ri(h,E)}return null}function y(m,h,p,E){for(var C=null,N=null,P=h,_=h=0,I=null;P!==null&&_<p.length;_++){P.index>_?(I=P,P=null):I=P.sibling;var F=v(m,P,p[_],E);if(F===null){P===null&&(P=I);break}e&&P&&F.alternate===null&&t(m,P),h=s(F,h,_),N===null?C=F:N.sibling=F,N=F,P=I}if(_===p.length)return n(m,P),H&&Qt(m,_),C;if(P===null){for(;_<p.length;_++)P=d(m,p[_],E),P!==null&&(h=s(P,h,_),N===null?C=P:N.sibling=P,N=P);return H&&Qt(m,_),C}for(P=r(m,P);_<p.length;_++)I=g(P,m,_,p[_],E),I!==null&&(e&&I.alternate!==null&&P.delete(I.key===null?_:I.key),h=s(I,h,_),N===null?C=I:N.sibling=I,N=I);return e&&P.forEach(function(ae){return t(m,ae)}),H&&Qt(m,_),C}function x(m,h,p,E){var C=Bn(p);if(typeof C!="function")throw Error(k(150));if(p=C.call(p),p==null)throw Error(k(151));for(var N=C=null,P=h,_=h=0,I=null,F=p.next();P!==null&&!F.done;_++,F=p.next()){P.index>_?(I=P,P=null):I=P.sibling;var ae=v(m,P,F.value,E);if(ae===null){P===null&&(P=I);break}e&&P&&ae.alternate===null&&t(m,P),h=s(ae,h,_),N===null?C=ae:N.sibling=ae,N=ae,P=I}if(F.done)return n(m,P),H&&Qt(m,_),C;if(P===null){for(;!F.done;_++,F=p.next())F=d(m,F.value,E),F!==null&&(h=s(F,h,_),N===null?C=F:N.sibling=F,N=F);return H&&Qt(m,_),C}for(P=r(m,P);!F.done;_++,F=p.next())F=g(P,m,_,F.value,E),F!==null&&(e&&F.alternate!==null&&P.delete(F.key===null?_:F.key),h=s(F,h,_),N===null?C=F:N.sibling=F,N=F);return e&&P.forEach(function(Oe){return t(m,Oe)}),H&&Qt(m,_),C}function S(m,h,p,E){if(typeof p=="object"&&p!==null&&p.type===cn&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case Vr:e:{for(var C=p.key,N=h;N!==null;){if(N.key===C){if(C=p.type,C===cn){if(N.tag===7){n(m,N.sibling),h=i(N,p.props.children),h.return=m,m=h;break e}}else if(N.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===yt&&su(C)===N.type){n(m,N.sibling),h=i(N,p.props),h.ref=Vn(m,N,p),h.return=m,m=h;break e}n(m,N);break}else t(m,N);N=N.sibling}p.type===cn?(h=Xt(p.props.children,m.mode,E,p.key),h.return=m,m=h):(E=xi(p.type,p.key,p.props,null,m.mode,E),E.ref=Vn(m,h,p),E.return=m,m=E)}return l(m);case un:e:{for(N=p.key;h!==null;){if(h.key===N)if(h.tag===4&&h.stateNode.containerInfo===p.containerInfo&&h.stateNode.implementation===p.implementation){n(m,h.sibling),h=i(h,p.children||[]),h.return=m,m=h;break e}else{n(m,h);break}else t(m,h);h=h.sibling}h=il(p,m.mode,E),h.return=m,m=h}return l(m);case yt:return N=p._init,S(m,h,N(p._payload),E)}if(Gn(p))return y(m,h,p,E);if(Bn(p))return x(m,h,p,E);ri(m,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,h!==null&&h.tag===6?(n(m,h.sibling),h=i(h,p),h.return=m,m=h):(n(m,h),h=rl(p,m.mode,E),h.return=m,m=h),l(m)):n(m,h)}return S}var Tn=Sf(!0),Ef=Sf(!1),$i=Ut(null),Bi=null,gn=null,Bo=null;function Qo(){Bo=gn=Bi=null}function bo(e){var t=$i.current;b($i),e._currentValue=t}function zl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Nn(e,t){Bi=e,Bo=gn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ee=!0),e.firstContext=null)}function Be(e){var t=e._currentValue;if(Bo!==e)if(e={context:e,memoizedValue:t,next:null},gn===null){if(Bi===null)throw Error(k(308));gn=e,Bi.dependencies={lanes:0,firstContext:e}}else gn=gn.next=e;return t}var qt=null;function Ho(e){qt===null?qt=[e]:qt.push(e)}function Cf(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Ho(t)):(n.next=i.next,i.next=n),t.interleaved=n,dt(e,r)}function dt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var gt=!1;function qo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function kf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ut(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ot(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,M&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,dt(e,n)}return i=r.interleaved,i===null?(t.next=t,Ho(r)):(t.next=i.next,i.next=t),r.interleaved=t,dt(e,n)}function hi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,_o(e,n)}}function lu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=l:s=s.next=l,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Qi(e,t,n,r){var i=e.updateQueue;gt=!1;var s=i.firstBaseUpdate,l=i.lastBaseUpdate,o=i.shared.pending;if(o!==null){i.shared.pending=null;var a=o,u=a.next;a.next=null,l===null?s=u:l.next=u,l=a;var f=e.alternate;f!==null&&(f=f.updateQueue,o=f.lastBaseUpdate,o!==l&&(o===null?f.firstBaseUpdate=u:o.next=u,f.lastBaseUpdate=a))}if(s!==null){var d=i.baseState;l=0,f=u=a=null,o=s;do{var v=o.lane,g=o.eventTime;if((r&v)===v){f!==null&&(f=f.next={eventTime:g,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var y=e,x=o;switch(v=t,g=n,x.tag){case 1:if(y=x.payload,typeof y=="function"){d=y.call(g,d,v);break e}d=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=x.payload,v=typeof y=="function"?y.call(g,d,v):y,v==null)break e;d=W({},d,v);break e;case 2:gt=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,v=i.effects,v===null?i.effects=[o]:v.push(o))}else g={eventTime:g,lane:v,tag:o.tag,payload:o.payload,callback:o.callback,next:null},f===null?(u=f=g,a=d):f=f.next=g,l|=v;if(o=o.next,o===null){if(o=i.shared.pending,o===null)break;v=o,o=v.next,v.next=null,i.lastBaseUpdate=v,i.shared.pending=null}}while(!0);if(f===null&&(a=d),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=f,t=i.shared.interleaved,t!==null){i=t;do l|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);tn|=l,e.lanes=l,e.memoizedState=d}}function ou(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(k(191,i));i.call(r)}}}var Mr={},rt=Ut(Mr),Cr=Ut(Mr),kr=Ut(Mr);function Vt(e){if(e===Mr)throw Error(k(174));return e}function Vo(e,t){switch($(kr,t),$(Cr,e),$(rt,Mr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:xl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=xl(t,e)}b(rt),$(rt,t)}function Fn(){b(rt),b(Cr),b(kr)}function Nf(e){Vt(kr.current);var t=Vt(rt.current),n=xl(t,e.type);t!==n&&($(Cr,e),$(rt,n))}function Wo(e){Cr.current===e&&(b(rt),b(Cr))}var q=Ut(0);function bi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Gs=[];function Ko(){for(var e=0;e<Gs.length;e++)Gs[e]._workInProgressVersionPrimary=null;Gs.length=0}var pi=pt.ReactCurrentDispatcher,Ys=pt.ReactCurrentBatchConfig,en=0,V=null,te=null,re=null,Hi=!1,lr=!1,Nr=0,am=0;function ue(){throw Error(k(321))}function Jo(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Xe(e[n],t[n]))return!1;return!0}function Xo(e,t,n,r,i,s){if(en=s,V=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,pi.current=e===null||e.memoizedState===null?dm:hm,e=n(r,i),lr){s=0;do{if(lr=!1,Nr=0,25<=s)throw Error(k(301));s+=1,re=te=null,t.updateQueue=null,pi.current=pm,e=n(r,i)}while(lr)}if(pi.current=qi,t=te!==null&&te.next!==null,en=0,re=te=V=null,Hi=!1,t)throw Error(k(300));return e}function Go(){var e=Nr!==0;return Nr=0,e}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return re===null?V.memoizedState=re=e:re=re.next=e,re}function Qe(){if(te===null){var e=V.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=re===null?V.memoizedState:re.next;if(t!==null)re=t,te=e;else{if(e===null)throw Error(k(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},re===null?V.memoizedState=re=e:re=re.next=e}return re}function Rr(e,t){return typeof t=="function"?t(e):t}function Zs(e){var t=Qe(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=te,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var l=i.next;i.next=s.next,s.next=l}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var o=l=null,a=null,u=s;do{var f=u.lane;if((en&f)===f)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(o=a=d,l=r):a=a.next=d,V.lanes|=f,tn|=f}u=u.next}while(u!==null&&u!==s);a===null?l=r:a.next=o,Xe(r,t.memoizedState)||(Ee=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,V.lanes|=s,tn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function el(e){var t=Qe(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var l=i=i.next;do s=e(s,l.action),l=l.next;while(l!==i);Xe(s,t.memoizedState)||(Ee=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Rf(){}function Pf(e,t){var n=V,r=Qe(),i=t(),s=!Xe(r.memoizedState,i);if(s&&(r.memoizedState=i,Ee=!0),r=r.queue,Yo(_f.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||re!==null&&re.memoizedState.tag&1){if(n.flags|=2048,Pr(9,Of.bind(null,n,r,i,t),void 0,null),ie===null)throw Error(k(349));en&30||jf(n,t,i)}return i}function jf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=V.updateQueue,t===null?(t={lastEffect:null,stores:null},V.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Of(e,t,n,r){t.value=n,t.getSnapshot=r,Tf(t)&&Ff(e)}function _f(e,t,n){return n(function(){Tf(t)&&Ff(e)})}function Tf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Xe(e,n)}catch{return!0}}function Ff(e){var t=dt(e,1);t!==null&&Je(t,e,1,-1)}function au(e){var t=et();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Rr,lastRenderedState:e},t.queue=e,e=e.dispatch=fm.bind(null,V,e),[t.memoizedState,e]}function Pr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=V.updateQueue,t===null?(t={lastEffect:null,stores:null},V.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Lf(){return Qe().memoizedState}function mi(e,t,n,r){var i=et();V.flags|=e,i.memoizedState=Pr(1|t,n,void 0,r===void 0?null:r)}function ps(e,t,n,r){var i=Qe();r=r===void 0?null:r;var s=void 0;if(te!==null){var l=te.memoizedState;if(s=l.destroy,r!==null&&Jo(r,l.deps)){i.memoizedState=Pr(t,n,s,r);return}}V.flags|=e,i.memoizedState=Pr(1|t,n,s,r)}function uu(e,t){return mi(8390656,8,e,t)}function Yo(e,t){return ps(2048,8,e,t)}function Af(e,t){return ps(4,2,e,t)}function Df(e,t){return ps(4,4,e,t)}function Mf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Uf(e,t,n){return n=n!=null?n.concat([e]):null,ps(4,4,Mf.bind(null,t,e),n)}function Zo(){}function If(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Jo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function zf(e,t){var n=Qe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Jo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function $f(e,t,n){return en&21?(Xe(n,t)||(n=Hc(),V.lanes|=n,tn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ee=!0),e.memoizedState=n)}function um(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=Ys.transition;Ys.transition={};try{e(!1),t()}finally{z=n,Ys.transition=r}}function Bf(){return Qe().memoizedState}function cm(e,t,n){var r=Tt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Qf(e))bf(t,n);else if(n=Cf(e,t,n,r),n!==null){var i=ye();Je(n,e,r,i),Hf(n,t,r)}}function fm(e,t,n){var r=Tt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Qf(e))bf(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var l=t.lastRenderedState,o=s(l,n);if(i.hasEagerState=!0,i.eagerState=o,Xe(o,l)){var a=t.interleaved;a===null?(i.next=i,Ho(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=Cf(e,t,i,r),n!==null&&(i=ye(),Je(n,e,r,i),Hf(n,t,r))}}function Qf(e){var t=e.alternate;return e===V||t!==null&&t===V}function bf(e,t){lr=Hi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Hf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,_o(e,n)}}var qi={readContext:Be,useCallback:ue,useContext:ue,useEffect:ue,useImperativeHandle:ue,useInsertionEffect:ue,useLayoutEffect:ue,useMemo:ue,useReducer:ue,useRef:ue,useState:ue,useDebugValue:ue,useDeferredValue:ue,useTransition:ue,useMutableSource:ue,useSyncExternalStore:ue,useId:ue,unstable_isNewReconciler:!1},dm={readContext:Be,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:Be,useEffect:uu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,mi(4194308,4,Mf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return mi(4194308,4,e,t)},useInsertionEffect:function(e,t){return mi(4,2,e,t)},useMemo:function(e,t){var n=et();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=et();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=cm.bind(null,V,e),[r.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:au,useDebugValue:Zo,useDeferredValue:function(e){return et().memoizedState=e},useTransition:function(){var e=au(!1),t=e[0];return e=um.bind(null,e[1]),et().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=V,i=et();if(H){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),ie===null)throw Error(k(349));en&30||jf(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,uu(_f.bind(null,r,s,e),[e]),r.flags|=2048,Pr(9,Of.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=et(),t=ie.identifierPrefix;if(H){var n=at,r=ot;n=(r&~(1<<32-Ke(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Nr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=am++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},hm={readContext:Be,useCallback:If,useContext:Be,useEffect:Yo,useImperativeHandle:Uf,useInsertionEffect:Af,useLayoutEffect:Df,useMemo:zf,useReducer:Zs,useRef:Lf,useState:function(){return Zs(Rr)},useDebugValue:Zo,useDeferredValue:function(e){var t=Qe();return $f(t,te.memoizedState,e)},useTransition:function(){var e=Zs(Rr)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:Rf,useSyncExternalStore:Pf,useId:Bf,unstable_isNewReconciler:!1},pm={readContext:Be,useCallback:If,useContext:Be,useEffect:Yo,useImperativeHandle:Uf,useInsertionEffect:Af,useLayoutEffect:Df,useMemo:zf,useReducer:el,useRef:Lf,useState:function(){return el(Rr)},useDebugValue:Zo,useDeferredValue:function(e){var t=Qe();return te===null?t.memoizedState=e:$f(t,te.memoizedState,e)},useTransition:function(){var e=el(Rr)[0],t=Qe().memoizedState;return[e,t]},useMutableSource:Rf,useSyncExternalStore:Pf,useId:Bf,unstable_isNewReconciler:!1};function qe(e,t){if(e&&e.defaultProps){t=W({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function $l(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:W({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ms={isMounted:function(e){return(e=e._reactInternals)?ln(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ye(),i=Tt(e),s=ut(r,i);s.payload=t,n!=null&&(s.callback=n),t=Ot(e,s,i),t!==null&&(Je(t,e,i,r),hi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ye(),i=Tt(e),s=ut(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=Ot(e,s,i),t!==null&&(Je(t,e,i,r),hi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ye(),r=Tt(e),i=ut(n,r);i.tag=2,t!=null&&(i.callback=t),t=Ot(e,i,r),t!==null&&(Je(t,e,r,n),hi(t,e,r))}};function cu(e,t,n,r,i,s,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,l):t.prototype&&t.prototype.isPureReactComponent?!xr(n,r)||!xr(i,s):!0}function qf(e,t,n){var r=!1,i=Dt,s=t.contextType;return typeof s=="object"&&s!==null?s=Be(s):(i=ke(t)?Yt:me.current,r=t.contextTypes,s=(r=r!=null)?On(e,i):Dt),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ms,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function fu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ms.enqueueReplaceState(t,t.state,null)}function Bl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},qo(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Be(s):(s=ke(t)?Yt:me.current,i.context=On(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&($l(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&ms.enqueueReplaceState(i,i.state,null),Qi(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Ln(e,t){try{var n="",r=t;do n+=Qh(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function tl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ql(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var mm=typeof WeakMap=="function"?WeakMap:Map;function Vf(e,t,n){n=ut(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Wi||(Wi=!0,Yl=r),Ql(e,t)},n}function Wf(e,t,n){n=ut(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Ql(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Ql(e,t),typeof r!="function"&&(_t===null?_t=new Set([this]):_t.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function du(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new mm;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Om.bind(null,e,t,n),t.then(e,e))}function hu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function pu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ut(-1,1),t.tag=2,Ot(n,t,1))),n.lanes|=1),e)}var vm=pt.ReactCurrentOwner,Ee=!1;function ve(e,t,n,r){t.child=e===null?Ef(t,null,n,r):Tn(t,e.child,n,r)}function mu(e,t,n,r,i){n=n.render;var s=t.ref;return Nn(t,i),r=Xo(e,t,n,r,s,i),n=Go(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ht(e,t,i)):(H&&n&&Io(t),t.flags|=1,ve(e,t,r,i),t.child)}function vu(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!oa(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Kf(e,t,s,r,i)):(e=xi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var l=s.memoizedProps;if(n=n.compare,n=n!==null?n:xr,n(l,r)&&e.ref===t.ref)return ht(e,t,i)}return t.flags|=1,e=Ft(s,r),e.ref=t.ref,e.return=t,t.child=e}function Kf(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(xr(s,r)&&e.ref===t.ref)if(Ee=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Ee=!0);else return t.lanes=e.lanes,ht(e,t,i)}return bl(e,t,n,r,i)}function Jf(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},$(wn,Te),Te|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,$(wn,Te),Te|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,$(wn,Te),Te|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,$(wn,Te),Te|=r;return ve(e,t,i,n),t.child}function Xf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function bl(e,t,n,r,i){var s=ke(n)?Yt:me.current;return s=On(t,s),Nn(t,i),n=Xo(e,t,n,r,s,i),r=Go(),e!==null&&!Ee?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ht(e,t,i)):(H&&r&&Io(t),t.flags|=1,ve(e,t,n,i),t.child)}function yu(e,t,n,r,i){if(ke(n)){var s=!0;Ui(t)}else s=!1;if(Nn(t,i),t.stateNode===null)vi(e,t),qf(t,n,r),Bl(t,n,r,i),r=!0;else if(e===null){var l=t.stateNode,o=t.memoizedProps;l.props=o;var a=l.context,u=n.contextType;typeof u=="object"&&u!==null?u=Be(u):(u=ke(n)?Yt:me.current,u=On(t,u));var f=n.getDerivedStateFromProps,d=typeof f=="function"||typeof l.getSnapshotBeforeUpdate=="function";d||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(o!==r||a!==u)&&fu(t,l,r,u),gt=!1;var v=t.memoizedState;l.state=v,Qi(t,r,l,i),a=t.memoizedState,o!==r||v!==a||Ce.current||gt?(typeof f=="function"&&($l(t,n,f,r),a=t.memoizedState),(o=gt||cu(t,n,o,r,v,a,u))?(d||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),l.props=r,l.state=a,l.context=u,r=o):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,kf(e,t),o=t.memoizedProps,u=t.type===t.elementType?o:qe(t.type,o),l.props=u,d=t.pendingProps,v=l.context,a=n.contextType,typeof a=="object"&&a!==null?a=Be(a):(a=ke(n)?Yt:me.current,a=On(t,a));var g=n.getDerivedStateFromProps;(f=typeof g=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(o!==d||v!==a)&&fu(t,l,r,a),gt=!1,v=t.memoizedState,l.state=v,Qi(t,r,l,i);var y=t.memoizedState;o!==d||v!==y||Ce.current||gt?(typeof g=="function"&&($l(t,n,g,r),y=t.memoizedState),(u=gt||cu(t,n,u,r,v,y,a)||!1)?(f||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,y,a),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,y,a)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||o===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),l.props=r,l.state=y,l.context=a,r=u):(typeof l.componentDidUpdate!="function"||o===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),r=!1)}return Hl(e,t,n,r,s,i)}function Hl(e,t,n,r,i,s){Xf(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return i&&nu(t,n,!1),ht(e,t,s);r=t.stateNode,vm.current=t;var o=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=Tn(t,e.child,null,s),t.child=Tn(t,null,o,s)):ve(e,t,o,s),t.memoizedState=r.state,i&&nu(t,n,!0),t.child}function Gf(e){var t=e.stateNode;t.pendingContext?tu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&tu(e,t.context,!1),Vo(e,t.containerInfo)}function gu(e,t,n,r,i){return _n(),$o(i),t.flags|=256,ve(e,t,n,r),t.child}var ql={dehydrated:null,treeContext:null,retryLane:0};function Vl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Yf(e,t,n){var r=t.pendingProps,i=q.current,s=!1,l=(t.flags&128)!==0,o;if((o=l)||(o=e!==null&&e.memoizedState===null?!1:(i&2)!==0),o?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),$(q,i&1),e===null)return Il(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,s?(r=t.mode,s=t.child,l={mode:"hidden",children:l},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=l):s=gs(l,r,0,null),e=Xt(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Vl(n),t.memoizedState=ql,e):ea(t,l));if(i=e.memoizedState,i!==null&&(o=i.dehydrated,o!==null))return ym(e,t,l,r,o,i,n);if(s){s=r.fallback,l=t.mode,i=e.child,o=i.sibling;var a={mode:"hidden",children:r.children};return!(l&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Ft(i,a),r.subtreeFlags=i.subtreeFlags&14680064),o!==null?s=Ft(o,s):(s=Xt(s,l,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,l=e.child.memoizedState,l=l===null?Vl(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=ql,r}return s=e.child,e=s.sibling,r=Ft(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ea(e,t){return t=gs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ii(e,t,n,r){return r!==null&&$o(r),Tn(t,e.child,null,n),e=ea(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ym(e,t,n,r,i,s,l){if(n)return t.flags&256?(t.flags&=-257,r=tl(Error(k(422))),ii(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=gs({mode:"visible",children:r.children},i,0,null),s=Xt(s,i,l,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Tn(t,e.child,null,l),t.child.memoizedState=Vl(l),t.memoizedState=ql,s);if(!(t.mode&1))return ii(e,t,l,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var o=r.dgst;return r=o,s=Error(k(419)),r=tl(s,r,void 0),ii(e,t,l,r)}if(o=(l&e.childLanes)!==0,Ee||o){if(r=ie,r!==null){switch(l&-l){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|l)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,dt(e,i),Je(r,e,i,-1))}return la(),r=tl(Error(k(421))),ii(e,t,l,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=_m.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Fe=jt(i.nextSibling),Le=t,H=!0,We=null,e!==null&&(Ue[Ie++]=ot,Ue[Ie++]=at,Ue[Ie++]=Zt,ot=e.id,at=e.overflow,Zt=t),t=ea(t,r.children),t.flags|=4096,t)}function xu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),zl(e.return,t,n)}function nl(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function Zf(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(ve(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xu(e,n,t);else if(e.tag===19)xu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if($(q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&bi(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),nl(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&bi(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}nl(t,!0,n,null,s);break;case"together":nl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function vi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ht(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),tn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=Ft(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ft(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function gm(e,t,n){switch(t.tag){case 3:Gf(t),_n();break;case 5:Nf(t);break;case 1:ke(t.type)&&Ui(t);break;case 4:Vo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;$($i,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?($(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?Yf(e,t,n):($(q,q.current&1),e=ht(e,t,n),e!==null?e.sibling:null);$(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Zf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),$(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,Jf(e,t,n)}return ht(e,t,n)}var ed,Wl,td,nd;ed=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Wl=function(){};td=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Vt(rt.current);var s=null;switch(n){case"input":i=ml(e,i),r=ml(e,r),s=[];break;case"select":i=W({},i,{value:void 0}),r=W({},r,{value:void 0}),s=[];break;case"textarea":i=gl(e,i),r=gl(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Di)}wl(n,r);var l;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var o=i[u];for(l in o)o.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(dr.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(o=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==o&&(a!=null||o!=null))if(u==="style")if(o){for(l in o)!o.hasOwnProperty(l)||a&&a.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in a)a.hasOwnProperty(l)&&o[l]!==a[l]&&(n||(n={}),n[l]=a[l])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,o=o?o.__html:void 0,a!=null&&o!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(dr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&Q("scroll",e),s||o===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};nd=function(e,t,n,r){n!==r&&(t.flags|=4)};function Wn(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ce(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function xm(e,t,n){var r=t.pendingProps;switch(zo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ce(t),null;case 1:return ke(t.type)&&Mi(),ce(t),null;case 3:return r=t.stateNode,Fn(),b(Ce),b(me),Ko(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ni(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,We!==null&&(to(We),We=null))),Wl(e,t),ce(t),null;case 5:Wo(t);var i=Vt(kr.current);if(n=t.type,e!==null&&t.stateNode!=null)td(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return ce(t),null}if(e=Vt(rt.current),ni(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[tt]=t,r[Er]=s,e=(t.mode&1)!==0,n){case"dialog":Q("cancel",r),Q("close",r);break;case"iframe":case"object":case"embed":Q("load",r);break;case"video":case"audio":for(i=0;i<Zn.length;i++)Q(Zn[i],r);break;case"source":Q("error",r);break;case"img":case"image":case"link":Q("error",r),Q("load",r);break;case"details":Q("toggle",r);break;case"input":ja(r,s),Q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},Q("invalid",r);break;case"textarea":_a(r,s),Q("invalid",r)}wl(n,s),i=null;for(var l in s)if(s.hasOwnProperty(l)){var o=s[l];l==="children"?typeof o=="string"?r.textContent!==o&&(s.suppressHydrationWarning!==!0&&ti(r.textContent,o,e),i=["children",o]):typeof o=="number"&&r.textContent!==""+o&&(s.suppressHydrationWarning!==!0&&ti(r.textContent,o,e),i=["children",""+o]):dr.hasOwnProperty(l)&&o!=null&&l==="onScroll"&&Q("scroll",r)}switch(n){case"input":Wr(r),Oa(r,s,!0);break;case"textarea":Wr(r),Ta(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Di)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Oc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[tt]=t,e[Er]=r,ed(e,t,!1,!1),t.stateNode=e;e:{switch(l=Sl(n,r),n){case"dialog":Q("cancel",e),Q("close",e),i=r;break;case"iframe":case"object":case"embed":Q("load",e),i=r;break;case"video":case"audio":for(i=0;i<Zn.length;i++)Q(Zn[i],e);i=r;break;case"source":Q("error",e),i=r;break;case"img":case"image":case"link":Q("error",e),Q("load",e),i=r;break;case"details":Q("toggle",e),i=r;break;case"input":ja(e,r),i=ml(e,r),Q("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=W({},r,{value:void 0}),Q("invalid",e);break;case"textarea":_a(e,r),i=gl(e,r),Q("invalid",e);break;default:i=r}wl(n,i),o=i;for(s in o)if(o.hasOwnProperty(s)){var a=o[s];s==="style"?Fc(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&_c(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&hr(e,a):typeof a=="number"&&hr(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(dr.hasOwnProperty(s)?a!=null&&s==="onScroll"&&Q("scroll",e):a!=null&&ko(e,s,a,l))}switch(n){case"input":Wr(e),Oa(e,r,!1);break;case"textarea":Wr(e),Ta(e);break;case"option":r.value!=null&&e.setAttribute("value",""+At(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Sn(e,!!r.multiple,s,!1):r.defaultValue!=null&&Sn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Di)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ce(t),null;case 6:if(e&&t.stateNode!=null)nd(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=Vt(kr.current),Vt(rt.current),ni(t)){if(r=t.stateNode,n=t.memoizedProps,r[tt]=t,(s=r.nodeValue!==n)&&(e=Le,e!==null))switch(e.tag){case 3:ti(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ti(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[tt]=t,t.stateNode=r}return ce(t),null;case 13:if(b(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Fe!==null&&t.mode&1&&!(t.flags&128))wf(),_n(),t.flags|=98560,s=!1;else if(s=ni(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(k(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(k(317));s[tt]=t}else _n(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ce(t),s=!1}else We!==null&&(to(We),We=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?ne===0&&(ne=3):la())),t.updateQueue!==null&&(t.flags|=4),ce(t),null);case 4:return Fn(),Wl(e,t),e===null&&wr(t.stateNode.containerInfo),ce(t),null;case 10:return bo(t.type._context),ce(t),null;case 17:return ke(t.type)&&Mi(),ce(t),null;case 19:if(b(q),s=t.memoizedState,s===null)return ce(t),null;if(r=(t.flags&128)!==0,l=s.rendering,l===null)if(r)Wn(s,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=bi(e),l!==null){for(t.flags|=128,Wn(s,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,l=s.alternate,l===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=l.childLanes,s.lanes=l.lanes,s.child=l.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=l.memoizedProps,s.memoizedState=l.memoizedState,s.updateQueue=l.updateQueue,s.type=l.type,e=l.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return $(q,q.current&1|2),t.child}e=e.sibling}s.tail!==null&&X()>An&&(t.flags|=128,r=!0,Wn(s,!1),t.lanes=4194304)}else{if(!r)if(e=bi(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Wn(s,!0),s.tail===null&&s.tailMode==="hidden"&&!l.alternate&&!H)return ce(t),null}else 2*X()-s.renderingStartTime>An&&n!==1073741824&&(t.flags|=128,r=!0,Wn(s,!1),t.lanes=4194304);s.isBackwards?(l.sibling=t.child,t.child=l):(n=s.last,n!==null?n.sibling=l:t.child=l,s.last=l)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=X(),t.sibling=null,n=q.current,$(q,r?n&1|2:n&1),t):(ce(t),null);case 22:case 23:return sa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Te&1073741824&&(ce(t),t.subtreeFlags&6&&(t.flags|=8192)):ce(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function wm(e,t){switch(zo(t),t.tag){case 1:return ke(t.type)&&Mi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Fn(),b(Ce),b(me),Ko(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Wo(t),null;case 13:if(b(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));_n()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return b(q),null;case 4:return Fn(),null;case 10:return bo(t.type._context),null;case 22:case 23:return sa(),null;case 24:return null;default:return null}}var si=!1,de=!1,Sm=typeof WeakSet=="function"?WeakSet:Set,O=null;function xn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){K(e,t,r)}else n.current=null}function Kl(e,t,n){try{n()}catch(r){K(e,t,r)}}var wu=!1;function Em(e,t){if(Tl=Fi,e=of(),Uo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var l=0,o=-1,a=-1,u=0,f=0,d=e,v=null;t:for(;;){for(var g;d!==n||i!==0&&d.nodeType!==3||(o=l+i),d!==s||r!==0&&d.nodeType!==3||(a=l+r),d.nodeType===3&&(l+=d.nodeValue.length),(g=d.firstChild)!==null;)v=d,d=g;for(;;){if(d===e)break t;if(v===n&&++u===i&&(o=l),v===s&&++f===r&&(a=l),(g=d.nextSibling)!==null)break;d=v,v=d.parentNode}d=g}n=o===-1||a===-1?null:{start:o,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Fl={focusedElem:e,selectionRange:n},Fi=!1,O=t;O!==null;)if(t=O,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,O=e;else for(;O!==null;){t=O;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var x=y.memoizedProps,S=y.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?x:qe(t.type,x),S);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(E){K(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,O=e;break}O=t.return}return y=wu,wu=!1,y}function or(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&Kl(t,n,s)}i=i.next}while(i!==r)}}function vs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Jl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function rd(e){var t=e.alternate;t!==null&&(e.alternate=null,rd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tt],delete t[Er],delete t[Dl],delete t[im],delete t[sm])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function id(e){return e.tag===5||e.tag===3||e.tag===4}function Su(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||id(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Xl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Di));else if(r!==4&&(e=e.child,e!==null))for(Xl(e,t,n),e=e.sibling;e!==null;)Xl(e,t,n),e=e.sibling}function Gl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Gl(e,t,n),e=e.sibling;e!==null;)Gl(e,t,n),e=e.sibling}var se=null,Ve=!1;function vt(e,t,n){for(n=n.child;n!==null;)sd(e,t,n),n=n.sibling}function sd(e,t,n){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(as,n)}catch{}switch(n.tag){case 5:de||xn(n,t);case 6:var r=se,i=Ve;se=null,vt(e,t,n),se=r,Ve=i,se!==null&&(Ve?(e=se,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):se.removeChild(n.stateNode));break;case 18:se!==null&&(Ve?(e=se,n=n.stateNode,e.nodeType===8?Js(e.parentNode,n):e.nodeType===1&&Js(e,n),yr(e)):Js(se,n.stateNode));break;case 4:r=se,i=Ve,se=n.stateNode.containerInfo,Ve=!0,vt(e,t,n),se=r,Ve=i;break;case 0:case 11:case 14:case 15:if(!de&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,l=s.destroy;s=s.tag,l!==void 0&&(s&2||s&4)&&Kl(n,t,l),i=i.next}while(i!==r)}vt(e,t,n);break;case 1:if(!de&&(xn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(o){K(n,t,o)}vt(e,t,n);break;case 21:vt(e,t,n);break;case 22:n.mode&1?(de=(r=de)||n.memoizedState!==null,vt(e,t,n),de=r):vt(e,t,n);break;default:vt(e,t,n)}}function Eu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Sm),t.forEach(function(r){var i=Tm.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function He(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,l=t,o=l;e:for(;o!==null;){switch(o.tag){case 5:se=o.stateNode,Ve=!1;break e;case 3:se=o.stateNode.containerInfo,Ve=!0;break e;case 4:se=o.stateNode.containerInfo,Ve=!0;break e}o=o.return}if(se===null)throw Error(k(160));sd(s,l,i),se=null,Ve=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){K(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)ld(t,e),t=t.sibling}function ld(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(He(t,e),Ye(e),r&4){try{or(3,e,e.return),vs(3,e)}catch(x){K(e,e.return,x)}try{or(5,e,e.return)}catch(x){K(e,e.return,x)}}break;case 1:He(t,e),Ye(e),r&512&&n!==null&&xn(n,n.return);break;case 5:if(He(t,e),Ye(e),r&512&&n!==null&&xn(n,n.return),e.flags&32){var i=e.stateNode;try{hr(i,"")}catch(x){K(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,l=n!==null?n.memoizedProps:s,o=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{o==="input"&&s.type==="radio"&&s.name!=null&&Pc(i,s),Sl(o,l);var u=Sl(o,s);for(l=0;l<a.length;l+=2){var f=a[l],d=a[l+1];f==="style"?Fc(i,d):f==="dangerouslySetInnerHTML"?_c(i,d):f==="children"?hr(i,d):ko(i,f,d,u)}switch(o){case"input":vl(i,s);break;case"textarea":jc(i,s);break;case"select":var v=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var g=s.value;g!=null?Sn(i,!!s.multiple,g,!1):v!==!!s.multiple&&(s.defaultValue!=null?Sn(i,!!s.multiple,s.defaultValue,!0):Sn(i,!!s.multiple,s.multiple?[]:"",!1))}i[Er]=s}catch(x){K(e,e.return,x)}}break;case 6:if(He(t,e),Ye(e),r&4){if(e.stateNode===null)throw Error(k(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(x){K(e,e.return,x)}}break;case 3:if(He(t,e),Ye(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{yr(t.containerInfo)}catch(x){K(e,e.return,x)}break;case 4:He(t,e),Ye(e);break;case 13:He(t,e),Ye(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(ra=X())),r&4&&Eu(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(de=(u=de)||f,He(t,e),de=u):He(t,e),Ye(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(O=e,f=e.child;f!==null;){for(d=O=f;O!==null;){switch(v=O,g=v.child,v.tag){case 0:case 11:case 14:case 15:or(4,v,v.return);break;case 1:xn(v,v.return);var y=v.stateNode;if(typeof y.componentWillUnmount=="function"){r=v,n=v.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(x){K(r,n,x)}}break;case 5:xn(v,v.return);break;case 22:if(v.memoizedState!==null){ku(d);continue}}g!==null?(g.return=v,O=g):ku(d)}f=f.sibling}e:for(f=null,d=e;;){if(d.tag===5){if(f===null){f=d;try{i=d.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(o=d.stateNode,a=d.memoizedProps.style,l=a!=null&&a.hasOwnProperty("display")?a.display:null,o.style.display=Tc("display",l))}catch(x){K(e,e.return,x)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(x){K(e,e.return,x)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:He(t,e),Ye(e),r&4&&Eu(e);break;case 21:break;default:He(t,e),Ye(e)}}function Ye(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(id(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(hr(i,""),r.flags&=-33);var s=Su(e);Gl(e,s,i);break;case 3:case 4:var l=r.stateNode.containerInfo,o=Su(e);Xl(e,o,l);break;default:throw Error(k(161))}}catch(a){K(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Cm(e,t,n){O=e,od(e)}function od(e,t,n){for(var r=(e.mode&1)!==0;O!==null;){var i=O,s=i.child;if(i.tag===22&&r){var l=i.memoizedState!==null||si;if(!l){var o=i.alternate,a=o!==null&&o.memoizedState!==null||de;o=si;var u=de;if(si=l,(de=a)&&!u)for(O=i;O!==null;)l=O,a=l.child,l.tag===22&&l.memoizedState!==null?Nu(i):a!==null?(a.return=l,O=a):Nu(i);for(;s!==null;)O=s,od(s),s=s.sibling;O=i,si=o,de=u}Cu(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,O=s):Cu(e)}}function Cu(e){for(;O!==null;){var t=O;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:de||vs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!de)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:qe(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&ou(t,s,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ou(t,l,n)}break;case 5:var o=t.stateNode;if(n===null&&t.flags&4){n=o;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&yr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}de||t.flags&512&&Jl(t)}catch(v){K(t,t.return,v)}}if(t===e){O=null;break}if(n=t.sibling,n!==null){n.return=t.return,O=n;break}O=t.return}}function ku(e){for(;O!==null;){var t=O;if(t===e){O=null;break}var n=t.sibling;if(n!==null){n.return=t.return,O=n;break}O=t.return}}function Nu(e){for(;O!==null;){var t=O;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{vs(4,t)}catch(a){K(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){K(t,i,a)}}var s=t.return;try{Jl(t)}catch(a){K(t,s,a)}break;case 5:var l=t.return;try{Jl(t)}catch(a){K(t,l,a)}}}catch(a){K(t,t.return,a)}if(t===e){O=null;break}var o=t.sibling;if(o!==null){o.return=t.return,O=o;break}O=t.return}}var km=Math.ceil,Vi=pt.ReactCurrentDispatcher,ta=pt.ReactCurrentOwner,$e=pt.ReactCurrentBatchConfig,M=0,ie=null,Z=null,le=0,Te=0,wn=Ut(0),ne=0,jr=null,tn=0,ys=0,na=0,ar=null,Se=null,ra=0,An=1/0,st=null,Wi=!1,Yl=null,_t=null,li=!1,Ct=null,Ki=0,ur=0,Zl=null,yi=-1,gi=0;function ye(){return M&6?X():yi!==-1?yi:yi=X()}function Tt(e){return e.mode&1?M&2&&le!==0?le&-le:om.transition!==null?(gi===0&&(gi=Hc()),gi):(e=z,e!==0||(e=window.event,e=e===void 0?16:Gc(e.type)),e):1}function Je(e,t,n,r){if(50<ur)throw ur=0,Zl=null,Error(k(185));Lr(e,n,r),(!(M&2)||e!==ie)&&(e===ie&&(!(M&2)&&(ys|=n),ne===4&&St(e,le)),Ne(e,r),n===1&&M===0&&!(t.mode&1)&&(An=X()+500,hs&&It()))}function Ne(e,t){var n=e.callbackNode;op(e,t);var r=Ti(e,e===ie?le:0);if(r===0)n!==null&&Aa(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Aa(n),t===1)e.tag===0?lm(Ru.bind(null,e)):yf(Ru.bind(null,e)),nm(function(){!(M&6)&&It()}),n=null;else{switch(qc(r)){case 1:n=Oo;break;case 4:n=Qc;break;case 16:n=_i;break;case 536870912:n=bc;break;default:n=_i}n=md(n,ad.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ad(e,t){if(yi=-1,gi=0,M&6)throw Error(k(327));var n=e.callbackNode;if(Rn()&&e.callbackNode!==n)return null;var r=Ti(e,e===ie?le:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ji(e,r);else{t=r;var i=M;M|=2;var s=cd();(ie!==e||le!==t)&&(st=null,An=X()+500,Jt(e,t));do try{Pm();break}catch(o){ud(e,o)}while(!0);Qo(),Vi.current=s,M=i,Z!==null?t=0:(ie=null,le=0,t=ne)}if(t!==0){if(t===2&&(i=Rl(e),i!==0&&(r=i,t=eo(e,i))),t===1)throw n=jr,Jt(e,0),St(e,r),Ne(e,X()),n;if(t===6)St(e,r);else{if(i=e.current.alternate,!(r&30)&&!Nm(i)&&(t=Ji(e,r),t===2&&(s=Rl(e),s!==0&&(r=s,t=eo(e,s))),t===1))throw n=jr,Jt(e,0),St(e,r),Ne(e,X()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:bt(e,Se,st);break;case 3:if(St(e,r),(r&130023424)===r&&(t=ra+500-X(),10<t)){if(Ti(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){ye(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Al(bt.bind(null,e,Se,st),t);break}bt(e,Se,st);break;case 4:if(St(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var l=31-Ke(r);s=1<<l,l=t[l],l>i&&(i=l),r&=~s}if(r=i,r=X()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*km(r/1960))-r,10<r){e.timeoutHandle=Al(bt.bind(null,e,Se,st),r);break}bt(e,Se,st);break;case 5:bt(e,Se,st);break;default:throw Error(k(329))}}}return Ne(e,X()),e.callbackNode===n?ad.bind(null,e):null}function eo(e,t){var n=ar;return e.current.memoizedState.isDehydrated&&(Jt(e,t).flags|=256),e=Ji(e,t),e!==2&&(t=Se,Se=n,t!==null&&to(t)),e}function to(e){Se===null?Se=e:Se.push.apply(Se,e)}function Nm(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!Xe(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function St(e,t){for(t&=~na,t&=~ys,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ke(t),r=1<<n;e[n]=-1,t&=~r}}function Ru(e){if(M&6)throw Error(k(327));Rn();var t=Ti(e,0);if(!(t&1))return Ne(e,X()),null;var n=Ji(e,t);if(e.tag!==0&&n===2){var r=Rl(e);r!==0&&(t=r,n=eo(e,r))}if(n===1)throw n=jr,Jt(e,0),St(e,t),Ne(e,X()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,bt(e,Se,st),Ne(e,X()),null}function ia(e,t){var n=M;M|=1;try{return e(t)}finally{M=n,M===0&&(An=X()+500,hs&&It())}}function nn(e){Ct!==null&&Ct.tag===0&&!(M&6)&&Rn();var t=M;M|=1;var n=$e.transition,r=z;try{if($e.transition=null,z=1,e)return e()}finally{z=r,$e.transition=n,M=t,!(M&6)&&It()}}function sa(){Te=wn.current,b(wn)}function Jt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,tm(n)),Z!==null)for(n=Z.return;n!==null;){var r=n;switch(zo(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Mi();break;case 3:Fn(),b(Ce),b(me),Ko();break;case 5:Wo(r);break;case 4:Fn();break;case 13:b(q);break;case 19:b(q);break;case 10:bo(r.type._context);break;case 22:case 23:sa()}n=n.return}if(ie=e,Z=e=Ft(e.current,null),le=Te=t,ne=0,jr=null,na=ys=tn=0,Se=ar=null,qt!==null){for(t=0;t<qt.length;t++)if(n=qt[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var l=s.next;s.next=i,r.next=l}n.pending=r}qt=null}return e}function ud(e,t){do{var n=Z;try{if(Qo(),pi.current=qi,Hi){for(var r=V.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Hi=!1}if(en=0,re=te=V=null,lr=!1,Nr=0,ta.current=null,n===null||n.return===null){ne=1,jr=t,Z=null;break}e:{var s=e,l=n.return,o=n,a=t;if(t=le,o.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,f=o,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var v=f.alternate;v?(f.updateQueue=v.updateQueue,f.memoizedState=v.memoizedState,f.lanes=v.lanes):(f.updateQueue=null,f.memoizedState=null)}var g=hu(l);if(g!==null){g.flags&=-257,pu(g,l,o,s,t),g.mode&1&&du(s,u,t),t=g,a=u;var y=t.updateQueue;if(y===null){var x=new Set;x.add(a),t.updateQueue=x}else y.add(a);break e}else{if(!(t&1)){du(s,u,t),la();break e}a=Error(k(426))}}else if(H&&o.mode&1){var S=hu(l);if(S!==null){!(S.flags&65536)&&(S.flags|=256),pu(S,l,o,s,t),$o(Ln(a,o));break e}}s=a=Ln(a,o),ne!==4&&(ne=2),ar===null?ar=[s]:ar.push(s),s=l;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var m=Vf(s,a,t);lu(s,m);break e;case 1:o=a;var h=s.type,p=s.stateNode;if(!(s.flags&128)&&(typeof h.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(_t===null||!_t.has(p)))){s.flags|=65536,t&=-t,s.lanes|=t;var E=Wf(s,o,t);lu(s,E);break e}}s=s.return}while(s!==null)}dd(n)}catch(C){t=C,Z===n&&n!==null&&(Z=n=n.return);continue}break}while(!0)}function cd(){var e=Vi.current;return Vi.current=qi,e===null?qi:e}function la(){(ne===0||ne===3||ne===2)&&(ne=4),ie===null||!(tn&268435455)&&!(ys&268435455)||St(ie,le)}function Ji(e,t){var n=M;M|=2;var r=cd();(ie!==e||le!==t)&&(st=null,Jt(e,t));do try{Rm();break}catch(i){ud(e,i)}while(!0);if(Qo(),M=n,Vi.current=r,Z!==null)throw Error(k(261));return ie=null,le=0,ne}function Rm(){for(;Z!==null;)fd(Z)}function Pm(){for(;Z!==null&&!Yh();)fd(Z)}function fd(e){var t=pd(e.alternate,e,Te);e.memoizedProps=e.pendingProps,t===null?dd(e):Z=t,ta.current=null}function dd(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=wm(n,t),n!==null){n.flags&=32767,Z=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,Z=null;return}}else if(n=xm(n,t,Te),n!==null){Z=n;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);ne===0&&(ne=5)}function bt(e,t,n){var r=z,i=$e.transition;try{$e.transition=null,z=1,jm(e,t,n,r)}finally{$e.transition=i,z=r}return null}function jm(e,t,n,r){do Rn();while(Ct!==null);if(M&6)throw Error(k(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(ap(e,s),e===ie&&(Z=ie=null,le=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||li||(li=!0,md(_i,function(){return Rn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=$e.transition,$e.transition=null;var l=z;z=1;var o=M;M|=4,ta.current=null,Em(e,n),ld(n,e),Kp(Fl),Fi=!!Tl,Fl=Tl=null,e.current=n,Cm(n),Zh(),M=o,z=l,$e.transition=s}else e.current=n;if(li&&(li=!1,Ct=e,Ki=i),s=e.pendingLanes,s===0&&(_t=null),np(n.stateNode),Ne(e,X()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Wi)throw Wi=!1,e=Yl,Yl=null,e;return Ki&1&&e.tag!==0&&Rn(),s=e.pendingLanes,s&1?e===Zl?ur++:(ur=0,Zl=e):ur=0,It(),null}function Rn(){if(Ct!==null){var e=qc(Ki),t=$e.transition,n=z;try{if($e.transition=null,z=16>e?16:e,Ct===null)var r=!1;else{if(e=Ct,Ct=null,Ki=0,M&6)throw Error(k(331));var i=M;for(M|=4,O=e.current;O!==null;){var s=O,l=s.child;if(O.flags&16){var o=s.deletions;if(o!==null){for(var a=0;a<o.length;a++){var u=o[a];for(O=u;O!==null;){var f=O;switch(f.tag){case 0:case 11:case 15:or(8,f,s)}var d=f.child;if(d!==null)d.return=f,O=d;else for(;O!==null;){f=O;var v=f.sibling,g=f.return;if(rd(f),f===u){O=null;break}if(v!==null){v.return=g,O=v;break}O=g}}}var y=s.alternate;if(y!==null){var x=y.child;if(x!==null){y.child=null;do{var S=x.sibling;x.sibling=null,x=S}while(x!==null)}}O=s}}if(s.subtreeFlags&2064&&l!==null)l.return=s,O=l;else e:for(;O!==null;){if(s=O,s.flags&2048)switch(s.tag){case 0:case 11:case 15:or(9,s,s.return)}var m=s.sibling;if(m!==null){m.return=s.return,O=m;break e}O=s.return}}var h=e.current;for(O=h;O!==null;){l=O;var p=l.child;if(l.subtreeFlags&2064&&p!==null)p.return=l,O=p;else e:for(l=h;O!==null;){if(o=O,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:vs(9,o)}}catch(C){K(o,o.return,C)}if(o===l){O=null;break e}var E=o.sibling;if(E!==null){E.return=o.return,O=E;break e}O=o.return}}if(M=i,It(),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(as,e)}catch{}r=!0}return r}finally{z=n,$e.transition=t}}return!1}function Pu(e,t,n){t=Ln(n,t),t=Vf(e,t,1),e=Ot(e,t,1),t=ye(),e!==null&&(Lr(e,1,t),Ne(e,t))}function K(e,t,n){if(e.tag===3)Pu(e,e,n);else for(;t!==null;){if(t.tag===3){Pu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(_t===null||!_t.has(r))){e=Ln(n,e),e=Wf(t,e,1),t=Ot(t,e,1),e=ye(),t!==null&&(Lr(t,1,e),Ne(t,e));break}}t=t.return}}function Om(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ye(),e.pingedLanes|=e.suspendedLanes&n,ie===e&&(le&n)===n&&(ne===4||ne===3&&(le&130023424)===le&&500>X()-ra?Jt(e,0):na|=n),Ne(e,t)}function hd(e,t){t===0&&(e.mode&1?(t=Xr,Xr<<=1,!(Xr&130023424)&&(Xr=4194304)):t=1);var n=ye();e=dt(e,t),e!==null&&(Lr(e,t,n),Ne(e,n))}function _m(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),hd(e,n)}function Tm(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),hd(e,n)}var pd;pd=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ce.current)Ee=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ee=!1,gm(e,t,n);Ee=!!(e.flags&131072)}else Ee=!1,H&&t.flags&1048576&&gf(t,zi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;vi(e,t),e=t.pendingProps;var i=On(t,me.current);Nn(t,n),i=Xo(null,t,r,e,i,n);var s=Go();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ke(r)?(s=!0,Ui(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,qo(t),i.updater=ms,t.stateNode=i,i._reactInternals=t,Bl(t,r,e,n),t=Hl(null,t,r,!0,s,n)):(t.tag=0,H&&s&&Io(t),ve(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(vi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Lm(r),e=qe(r,e),i){case 0:t=bl(null,t,r,e,n);break e;case 1:t=yu(null,t,r,e,n);break e;case 11:t=mu(null,t,r,e,n);break e;case 14:t=vu(null,t,r,qe(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),bl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),yu(e,t,r,i,n);case 3:e:{if(Gf(t),e===null)throw Error(k(387));r=t.pendingProps,s=t.memoizedState,i=s.element,kf(e,t),Qi(t,r,null,n);var l=t.memoizedState;if(r=l.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=Ln(Error(k(423)),t),t=gu(e,t,r,n,i);break e}else if(r!==i){i=Ln(Error(k(424)),t),t=gu(e,t,r,n,i);break e}else for(Fe=jt(t.stateNode.containerInfo.firstChild),Le=t,H=!0,We=null,n=Ef(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(_n(),r===i){t=ht(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return Nf(t),e===null&&Il(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,l=i.children,Ll(r,i)?l=null:s!==null&&Ll(r,s)&&(t.flags|=32),Xf(e,t),ve(e,t,l,n),t.child;case 6:return e===null&&Il(t),null;case 13:return Yf(e,t,n);case 4:return Vo(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Tn(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),mu(e,t,r,i,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,l=i.value,$($i,r._currentValue),r._currentValue=l,s!==null)if(Xe(s.value,l)){if(s.children===i.children&&!Ce.current){t=ht(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var o=s.dependencies;if(o!==null){l=s.child;for(var a=o.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=ut(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?a.next=a:(a.next=f.next,f.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),zl(s.return,n,t),o.lanes|=n;break}a=a.next}}else if(s.tag===10)l=s.type===t.type?null:s.child;else if(s.tag===18){if(l=s.return,l===null)throw Error(k(341));l.lanes|=n,o=l.alternate,o!==null&&(o.lanes|=n),zl(l,n,t),l=s.sibling}else l=s.child;if(l!==null)l.return=s;else for(l=s;l!==null;){if(l===t){l=null;break}if(s=l.sibling,s!==null){s.return=l.return,l=s;break}l=l.return}s=l}ve(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Nn(t,n),i=Be(i),r=r(i),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,i=qe(r,t.pendingProps),i=qe(r.type,i),vu(e,t,r,i,n);case 15:return Kf(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:qe(r,i),vi(e,t),t.tag=1,ke(r)?(e=!0,Ui(t)):e=!1,Nn(t,n),qf(t,r,i),Bl(t,r,i,n),Hl(null,t,r,!0,e,n);case 19:return Zf(e,t,n);case 22:return Jf(e,t,n)}throw Error(k(156,t.tag))};function md(e,t){return Bc(e,t)}function Fm(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new Fm(e,t,n,r)}function oa(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Lm(e){if(typeof e=="function")return oa(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ro)return 11;if(e===Po)return 14}return 2}function Ft(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function xi(e,t,n,r,i,s){var l=2;if(r=e,typeof e=="function")oa(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case cn:return Xt(n.children,i,s,t);case No:l=8,i|=8;break;case fl:return e=ze(12,n,t,i|2),e.elementType=fl,e.lanes=s,e;case dl:return e=ze(13,n,t,i),e.elementType=dl,e.lanes=s,e;case hl:return e=ze(19,n,t,i),e.elementType=hl,e.lanes=s,e;case kc:return gs(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Ec:l=10;break e;case Cc:l=9;break e;case Ro:l=11;break e;case Po:l=14;break e;case yt:l=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=ze(l,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function Xt(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function gs(e,t,n,r){return e=ze(22,e,r,t),e.elementType=kc,e.lanes=n,e.stateNode={isHidden:!1},e}function rl(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function il(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Am(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Is(0),this.expirationTimes=Is(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Is(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function aa(e,t,n,r,i,s,l,o,a){return e=new Am(e,t,n,o,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=ze(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},qo(s),e}function Dm(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:un,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function vd(e){if(!e)return Dt;e=e._reactInternals;e:{if(ln(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ke(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(ke(n))return vf(e,n,t)}return t}function yd(e,t,n,r,i,s,l,o,a){return e=aa(n,r,!0,e,i,s,l,o,a),e.context=vd(null),n=e.current,r=ye(),i=Tt(n),s=ut(r,i),s.callback=t??null,Ot(n,s,i),e.current.lanes=i,Lr(e,i,r),Ne(e,r),e}function xs(e,t,n,r){var i=t.current,s=ye(),l=Tt(i);return n=vd(n),t.context===null?t.context=n:t.pendingContext=n,t=ut(s,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ot(i,t,l),e!==null&&(Je(e,i,l,s),hi(e,i,l)),l}function Xi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ju(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ua(e,t){ju(e,t),(e=e.alternate)&&ju(e,t)}function Mm(){return null}var gd=typeof reportError=="function"?reportError:function(e){console.error(e)};function ca(e){this._internalRoot=e}ws.prototype.render=ca.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));xs(e,t,null,null)};ws.prototype.unmount=ca.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;nn(function(){xs(null,e,null,null)}),t[ft]=null}};function ws(e){this._internalRoot=e}ws.prototype.unstable_scheduleHydration=function(e){if(e){var t=Kc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<wt.length&&t!==0&&t<wt[n].priority;n++);wt.splice(n,0,e),n===0&&Xc(e)}};function fa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ss(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ou(){}function Um(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=Xi(l);s.call(u)}}var l=yd(t,r,e,0,null,!1,!1,"",Ou);return e._reactRootContainer=l,e[ft]=l.current,wr(e.nodeType===8?e.parentNode:e),nn(),l}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var o=r;r=function(){var u=Xi(a);o.call(u)}}var a=aa(e,0,!1,null,null,!1,!1,"",Ou);return e._reactRootContainer=a,e[ft]=a.current,wr(e.nodeType===8?e.parentNode:e),nn(function(){xs(t,a,n,r)}),a}function Es(e,t,n,r,i){var s=n._reactRootContainer;if(s){var l=s;if(typeof i=="function"){var o=i;i=function(){var a=Xi(l);o.call(a)}}xs(t,l,e,i)}else l=Um(n,t,e,i,r);return Xi(l)}Vc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Yn(t.pendingLanes);n!==0&&(_o(t,n|1),Ne(t,X()),!(M&6)&&(An=X()+500,It()))}break;case 13:nn(function(){var r=dt(e,1);if(r!==null){var i=ye();Je(r,e,1,i)}}),ua(e,1)}};To=function(e){if(e.tag===13){var t=dt(e,134217728);if(t!==null){var n=ye();Je(t,e,134217728,n)}ua(e,134217728)}};Wc=function(e){if(e.tag===13){var t=Tt(e),n=dt(e,t);if(n!==null){var r=ye();Je(n,e,t,r)}ua(e,t)}};Kc=function(){return z};Jc=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};Cl=function(e,t,n){switch(t){case"input":if(vl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=ds(r);if(!i)throw Error(k(90));Rc(r),vl(r,i)}}}break;case"textarea":jc(e,n);break;case"select":t=n.value,t!=null&&Sn(e,!!n.multiple,t,!1)}};Dc=ia;Mc=nn;var Im={usingClientEntryPoint:!1,Events:[Dr,pn,ds,Lc,Ac,ia]},Kn={findFiberByHostInstance:Ht,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},zm={bundleType:Kn.bundleType,version:Kn.version,rendererPackageName:Kn.rendererPackageName,rendererConfig:Kn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:pt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zc(e),e===null?null:e.stateNode},findFiberByHostInstance:Kn.findFiberByHostInstance||Mm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var oi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!oi.isDisabled&&oi.supportsFiber)try{as=oi.inject(zm),nt=oi}catch{}}De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Im;De.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!fa(t))throw Error(k(200));return Dm(e,t,null,n)};De.createRoot=function(e,t){if(!fa(e))throw Error(k(299));var n=!1,r="",i=gd;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=aa(e,1,!1,null,null,n,!1,r,i),e[ft]=t.current,wr(e.nodeType===8?e.parentNode:e),new ca(t)};De.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=zc(t),e=e===null?null:e.stateNode,e};De.flushSync=function(e){return nn(e)};De.hydrate=function(e,t,n){if(!Ss(t))throw Error(k(200));return Es(null,e,t,!0,n)};De.hydrateRoot=function(e,t,n){if(!fa(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",l=gd;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=yd(t,null,e,1,n??null,i,!1,s,l),e[ft]=t.current,wr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new ws(t)};De.render=function(e,t,n){if(!Ss(t))throw Error(k(200));return Es(null,e,t,!1,n)};De.unmountComponentAtNode=function(e){if(!Ss(e))throw Error(k(40));return e._reactRootContainer?(nn(function(){Es(null,null,e,!1,function(){e._reactRootContainer=null,e[ft]=null})}),!0):!1};De.unstable_batchedUpdates=ia;De.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ss(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return Es(e,t,n,!1,r)};De.version="18.3.1-next-f1338f8080-20240426";function xd(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(xd)}catch(e){console.error(e)}}xd(),gc.exports=De;var wd=gc.exports;const $m=lc(wd);var _u=wd;ul.createRoot=_u.createRoot,ul.hydrateRoot=_u.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Or(){return Or=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Or.apply(this,arguments)}var kt;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(kt||(kt={}));const Tu="popstate";function Bm(e){e===void 0&&(e={});function t(r,i){let{pathname:s,search:l,hash:o}=r.location;return no("",{pathname:s,search:l,hash:o},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Gi(i)}return bm(t,n,null,e)}function ee(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Sd(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Qm(){return Math.random().toString(36).substr(2,8)}function Fu(e,t){return{usr:e.state,key:e.key,idx:t}}function no(e,t,n,r){return n===void 0&&(n=null),Or({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?In(t):t,{state:n,key:t&&t.key||r||Qm()})}function Gi(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function In(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function bm(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,l=i.history,o=kt.Pop,a=null,u=f();u==null&&(u=0,l.replaceState(Or({},l.state,{idx:u}),""));function f(){return(l.state||{idx:null}).idx}function d(){o=kt.Pop;let S=f(),m=S==null?null:S-u;u=S,a&&a({action:o,location:x.location,delta:m})}function v(S,m){o=kt.Push;let h=no(x.location,S,m);u=f()+1;let p=Fu(h,u),E=x.createHref(h);try{l.pushState(p,"",E)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;i.location.assign(E)}s&&a&&a({action:o,location:x.location,delta:1})}function g(S,m){o=kt.Replace;let h=no(x.location,S,m);u=f();let p=Fu(h,u),E=x.createHref(h);l.replaceState(p,"",E),s&&a&&a({action:o,location:x.location,delta:0})}function y(S){let m=i.location.origin!=="null"?i.location.origin:i.location.href,h=typeof S=="string"?S:Gi(S);return h=h.replace(/ $/,"%20"),ee(m,"No window.location.(origin|href) available to create URL for href: "+h),new URL(h,m)}let x={get action(){return o},get location(){return e(i,l)},listen(S){if(a)throw new Error("A history only accepts one active listener");return i.addEventListener(Tu,d),a=S,()=>{i.removeEventListener(Tu,d),a=null}},createHref(S){return t(i,S)},createURL:y,encodeLocation(S){let m=y(S);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:v,replace:g,go(S){return l.go(S)}};return x}var Lu;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Lu||(Lu={}));function Hm(e,t,n){return n===void 0&&(n="/"),qm(e,t,n)}function qm(e,t,n,r){let i=typeof t=="string"?In(t):t,s=da(i.pathname||"/",n);if(s==null)return null;let l=Ed(e);Vm(l);let o=null;for(let a=0;o==null&&a<l.length;++a){let u=iv(s);o=tv(l[a],u)}return o}function Ed(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,l,o)=>{let a={relativePath:o===void 0?s.path||"":o,caseSensitive:s.caseSensitive===!0,childrenIndex:l,route:s};a.relativePath.startsWith("/")&&(ee(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=Lt([r,a.relativePath]),f=n.concat(a);s.children&&s.children.length>0&&(ee(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Ed(s.children,t,f,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:Zm(u,s.index),routesMeta:f})};return e.forEach((s,l)=>{var o;if(s.path===""||!((o=s.path)!=null&&o.includes("?")))i(s,l);else for(let a of Cd(s.path))i(s,l,a)}),t}function Cd(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let l=Cd(r.join("/")),o=[];return o.push(...l.map(a=>a===""?s:[s,a].join("/"))),i&&o.push(...l),o.map(a=>e.startsWith("/")&&a===""?"/":a)}function Vm(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:ev(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Wm=/^:[\w-]+$/,Km=3,Jm=2,Xm=1,Gm=10,Ym=-2,Au=e=>e==="*";function Zm(e,t){let n=e.split("/"),r=n.length;return n.some(Au)&&(r+=Ym),t&&(r+=Jm),n.filter(i=>!Au(i)).reduce((i,s)=>i+(Wm.test(s)?Km:s===""?Xm:Gm),r)}function ev(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function tv(e,t,n){let{routesMeta:r}=e,i={},s="/",l=[];for(let o=0;o<r.length;++o){let a=r[o],u=o===r.length-1,f=s==="/"?t:t.slice(s.length)||"/",d=nv({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},f),v=a.route;if(!d)return null;Object.assign(i,d.params),l.push({params:i,pathname:Lt([s,d.pathname]),pathnameBase:av(Lt([s,d.pathnameBase])),route:v}),d.pathnameBase!=="/"&&(s=Lt([s,d.pathnameBase]))}return l}function nv(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=rv(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],l=s.replace(/(.)\/+$/,"$1"),o=i.slice(1);return{params:r.reduce((u,f,d)=>{let{paramName:v,isOptional:g}=f;if(v==="*"){let x=o[d]||"";l=s.slice(0,s.length-x.length).replace(/(.)\/+$/,"$1")}const y=o[d];return g&&!y?u[v]=void 0:u[v]=(y||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:l,pattern:e}}function rv(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Sd(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(l,o,a)=>(r.push({paramName:o,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function iv(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Sd(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function da(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function sv(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?In(e):e;return{pathname:n?n.startsWith("/")?n:lv(n,t):t,search:uv(r),hash:cv(i)}}function lv(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function sl(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function ov(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function kd(e,t){let n=ov(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Nd(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=In(e):(i=Or({},e),ee(!i.pathname||!i.pathname.includes("?"),sl("?","pathname","search",i)),ee(!i.pathname||!i.pathname.includes("#"),sl("#","pathname","hash",i)),ee(!i.search||!i.search.includes("#"),sl("#","search","hash",i)));let s=e===""||i.pathname==="",l=s?"/":i.pathname,o;if(l==null)o=n;else{let d=t.length-1;if(!r&&l.startsWith("..")){let v=l.split("/");for(;v[0]==="..";)v.shift(),d-=1;i.pathname=v.join("/")}o=d>=0?t[d]:"/"}let a=sv(i,o),u=l&&l!=="/"&&l.endsWith("/"),f=(s||l===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(u||f)&&(a.pathname+="/"),a}const Lt=e=>e.join("/").replace(/\/\/+/g,"/"),av=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),uv=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,cv=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function fv(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Rd=["post","put","patch","delete"];new Set(Rd);const dv=["get",...Rd];new Set(dv);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function _r(){return _r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_r.apply(this,arguments)}const ha=j.createContext(null),hv=j.createContext(null),on=j.createContext(null),Cs=j.createContext(null),zt=j.createContext({outlet:null,matches:[],isDataRoute:!1}),Pd=j.createContext(null);function pv(e,t){let{relative:n}=t===void 0?{}:t;Ur()||ee(!1);let{basename:r,navigator:i}=j.useContext(on),{hash:s,pathname:l,search:o}=Od(e,{relative:n}),a=l;return r!=="/"&&(a=l==="/"?r:Lt([r,l])),i.createHref({pathname:a,search:o,hash:s})}function Ur(){return j.useContext(Cs)!=null}function Ir(){return Ur()||ee(!1),j.useContext(Cs).location}function jd(e){j.useContext(on).static||j.useLayoutEffect(e)}function mv(){let{isDataRoute:e}=j.useContext(zt);return e?Ov():vv()}function vv(){Ur()||ee(!1);let e=j.useContext(ha),{basename:t,future:n,navigator:r}=j.useContext(on),{matches:i}=j.useContext(zt),{pathname:s}=Ir(),l=JSON.stringify(kd(i,n.v7_relativeSplatPath)),o=j.useRef(!1);return jd(()=>{o.current=!0}),j.useCallback(function(u,f){if(f===void 0&&(f={}),!o.current)return;if(typeof u=="number"){r.go(u);return}let d=Nd(u,JSON.parse(l),s,f.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Lt([t,d.pathname])),(f.replace?r.replace:r.push)(d,f.state,f)},[t,r,l,s,e])}function yv(){let{matches:e}=j.useContext(zt),t=e[e.length-1];return t?t.params:{}}function Od(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=j.useContext(on),{matches:i}=j.useContext(zt),{pathname:s}=Ir(),l=JSON.stringify(kd(i,r.v7_relativeSplatPath));return j.useMemo(()=>Nd(e,JSON.parse(l),s,n==="path"),[e,l,s,n])}function gv(e,t){return xv(e,t)}function xv(e,t,n,r){Ur()||ee(!1);let{navigator:i}=j.useContext(on),{matches:s}=j.useContext(zt),l=s[s.length-1],o=l?l.params:{};l&&l.pathname;let a=l?l.pathnameBase:"/";l&&l.route;let u=Ir(),f;if(t){var d;let S=typeof t=="string"?In(t):t;a==="/"||(d=S.pathname)!=null&&d.startsWith(a)||ee(!1),f=S}else f=u;let v=f.pathname||"/",g=v;if(a!=="/"){let S=a.replace(/^\//,"").split("/");g="/"+v.replace(/^\//,"").split("/").slice(S.length).join("/")}let y=Hm(e,{pathname:g}),x=kv(y&&y.map(S=>Object.assign({},S,{params:Object.assign({},o,S.params),pathname:Lt([a,i.encodeLocation?i.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?a:Lt([a,i.encodeLocation?i.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),s,n,r);return t&&x?j.createElement(Cs.Provider,{value:{location:_r({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:kt.Pop}},x):x}function wv(){let e=jv(),t=fv(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return j.createElement(j.Fragment,null,j.createElement("h2",null,"Unexpected Application Error!"),j.createElement("h3",{style:{fontStyle:"italic"}},t),n?j.createElement("pre",{style:i},n):null,null)}const Sv=j.createElement(wv,null);class Ev extends j.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?j.createElement(zt.Provider,{value:this.props.routeContext},j.createElement(Pd.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Cv(e){let{routeContext:t,match:n,children:r}=e,i=j.useContext(ha);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),j.createElement(zt.Provider,{value:t},r)}function kv(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let l=e,o=(i=n)==null?void 0:i.errors;if(o!=null){let f=l.findIndex(d=>d.route.id&&(o==null?void 0:o[d.route.id])!==void 0);f>=0||ee(!1),l=l.slice(0,Math.min(l.length,f+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<l.length;f++){let d=l[f];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=f),d.route.id){let{loaderData:v,errors:g}=n,y=d.route.loader&&v[d.route.id]===void 0&&(!g||g[d.route.id]===void 0);if(d.route.lazy||y){a=!0,u>=0?l=l.slice(0,u+1):l=[l[0]];break}}}return l.reduceRight((f,d,v)=>{let g,y=!1,x=null,S=null;n&&(g=o&&d.route.id?o[d.route.id]:void 0,x=d.route.errorElement||Sv,a&&(u<0&&v===0?(_v("route-fallback"),y=!0,S=null):u===v&&(y=!0,S=d.route.hydrateFallbackElement||null)));let m=t.concat(l.slice(0,v+1)),h=()=>{let p;return g?p=x:y?p=S:d.route.Component?p=j.createElement(d.route.Component,null):d.route.element?p=d.route.element:p=f,j.createElement(Cv,{match:d,routeContext:{outlet:f,matches:m,isDataRoute:n!=null},children:p})};return n&&(d.route.ErrorBoundary||d.route.errorElement||v===0)?j.createElement(Ev,{location:n.location,revalidation:n.revalidation,component:x,error:g,children:h(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):h()},null)}var _d=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(_d||{}),Td=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Td||{});function Nv(e){let t=j.useContext(ha);return t||ee(!1),t}function Rv(e){let t=j.useContext(hv);return t||ee(!1),t}function Pv(e){let t=j.useContext(zt);return t||ee(!1),t}function Fd(e){let t=Pv(),n=t.matches[t.matches.length-1];return n.route.id||ee(!1),n.route.id}function jv(){var e;let t=j.useContext(Pd),n=Rv(),r=Fd();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Ov(){let{router:e}=Nv(_d.UseNavigateStable),t=Fd(Td.UseNavigateStable),n=j.useRef(!1);return jd(()=>{n.current=!0}),j.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,_r({fromRouteId:t},s)))},[e,t])}const Du={};function _v(e,t,n){Du[e]||(Du[e]=!0)}function Tv(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function wi(e){ee(!1)}function Fv(e){let{basename:t="/",children:n=null,location:r,navigationType:i=kt.Pop,navigator:s,static:l=!1,future:o}=e;Ur()&&ee(!1);let a=t.replace(/^\/*/,"/"),u=j.useMemo(()=>({basename:a,navigator:s,static:l,future:_r({v7_relativeSplatPath:!1},o)}),[a,o,s,l]);typeof r=="string"&&(r=In(r));let{pathname:f="/",search:d="",hash:v="",state:g=null,key:y="default"}=r,x=j.useMemo(()=>{let S=da(f,a);return S==null?null:{location:{pathname:S,search:d,hash:v,state:g,key:y},navigationType:i}},[a,f,d,v,g,y,i]);return x==null?null:j.createElement(on.Provider,{value:u},j.createElement(Cs.Provider,{children:n,value:x}))}function Lv(e){let{children:t,location:n}=e;return gv(ro(t),n)}new Promise(()=>{});function ro(e,t){t===void 0&&(t=[]);let n=[];return j.Children.forEach(e,(r,i)=>{if(!j.isValidElement(r))return;let s=[...t,i];if(r.type===j.Fragment){n.push.apply(n,ro(r.props.children,s));return}r.type!==wi&&ee(!1),!r.props.index||!r.props.children||ee(!1);let l={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(l.children=ro(r.props.children,s)),n.push(l)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function io(){return io=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},io.apply(this,arguments)}function Av(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function Dv(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Mv(e,t){return e.button===0&&(!t||t==="_self")&&!Dv(e)}const Uv=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Iv="6";try{window.__reactRouterVersion=Iv}catch{}const zv="startTransition",Mu=Oh[zv];function $v(e){let{basename:t,children:n,future:r,window:i}=e,s=j.useRef();s.current==null&&(s.current=Bm({window:i,v5Compat:!0}));let l=s.current,[o,a]=j.useState({action:l.action,location:l.location}),{v7_startTransition:u}=r||{},f=j.useCallback(d=>{u&&Mu?Mu(()=>a(d)):a(d)},[a,u]);return j.useLayoutEffect(()=>l.listen(f),[l,f]),j.useEffect(()=>Tv(r),[r]),j.createElement(Fv,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:l,future:r})}const Bv=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Qv=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Pn=j.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:s,replace:l,state:o,target:a,to:u,preventScrollReset:f,viewTransition:d}=t,v=Av(t,Uv),{basename:g}=j.useContext(on),y,x=!1;if(typeof u=="string"&&Qv.test(u)&&(y=u,Bv))try{let p=new URL(window.location.href),E=u.startsWith("//")?new URL(p.protocol+u):new URL(u),C=da(E.pathname,g);E.origin===p.origin&&C!=null?u=C+E.search+E.hash:x=!0}catch{}let S=pv(u,{relative:i}),m=bv(u,{replace:l,state:o,target:a,preventScrollReset:f,relative:i,viewTransition:d});function h(p){r&&r(p),p.defaultPrevented||m(p)}return j.createElement("a",io({},v,{href:y||S,onClick:x||s?r:h,ref:n,target:a}))});var Uu;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Uu||(Uu={}));var Iu;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Iu||(Iu={}));function bv(e,t){let{target:n,replace:r,state:i,preventScrollReset:s,relative:l,viewTransition:o}=t===void 0?{}:t,a=mv(),u=Ir(),f=Od(e,{relative:l});return j.useCallback(d=>{if(Mv(d,n)){d.preventDefault();let v=r!==void 0?r:Gi(u)===Gi(f);a(e,{replace:v,state:i,preventScrollReset:s,relative:l,viewTransition:o})}},[u,a,f,r,i,n,e,s,l,o])}function so(e,t){return so=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,r){return n.__proto__=r,n},so(e,t)}function zr(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,so(e,t)}var $r=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(r){var i=this,s=r||function(){};return this.listeners.push(s),this.onSubscribe(),function(){i.listeners=i.listeners.filter(function(l){return l!==s}),i.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}();function U(){return U=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},U.apply(null,arguments)}var Yi=typeof window>"u";function fe(){}function Hv(e,t){return typeof e=="function"?e(t):e}function lo(e){return typeof e=="number"&&e>=0&&e!==1/0}function Zi(e){return Array.isArray(e)?e:[e]}function Ld(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Si(e,t,n){return ks(e)?typeof t=="function"?U({},n,{queryKey:e,queryFn:t}):U({},t,{queryKey:e}):e}function xt(e,t,n){return ks(e)?[U({},t,{queryKey:e}),n]:[e||{},t]}function qv(e,t){if(e===!0&&t===!0||e==null&&t==null)return"all";if(e===!1&&t===!1)return"none";var n=e??!t;return n?"active":"inactive"}function zu(e,t){var n=e.active,r=e.exact,i=e.fetching,s=e.inactive,l=e.predicate,o=e.queryKey,a=e.stale;if(ks(o)){if(r){if(t.queryHash!==pa(o,t.options))return!1}else if(!es(t.queryKey,o))return!1}var u=qv(n,s);if(u==="none")return!1;if(u!=="all"){var f=t.isActive();if(u==="active"&&!f||u==="inactive"&&f)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||typeof i=="boolean"&&t.isFetching()!==i||l&&!l(t))}function $u(e,t){var n=e.exact,r=e.fetching,i=e.predicate,s=e.mutationKey;if(ks(s)){if(!t.options.mutationKey)return!1;if(n){if(Wt(t.options.mutationKey)!==Wt(s))return!1}else if(!es(t.options.mutationKey,s))return!1}return!(typeof r=="boolean"&&t.state.status==="loading"!==r||i&&!i(t))}function pa(e,t){var n=(t==null?void 0:t.queryKeyHashFn)||Wt;return n(e)}function Wt(e){var t=Zi(e);return Vv(t)}function Vv(e){return JSON.stringify(e,function(t,n){return oo(n)?Object.keys(n).sort().reduce(function(r,i){return r[i]=n[i],r},{}):n})}function es(e,t){return Ad(Zi(e),Zi(t))}function Ad(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(function(n){return!Ad(e[n],t[n])}):!1}function ts(e,t){if(e===t)return e;var n=Array.isArray(e)&&Array.isArray(t);if(n||oo(e)&&oo(t)){for(var r=n?e.length:Object.keys(e).length,i=n?t:Object.keys(t),s=i.length,l=n?[]:{},o=0,a=0;a<s;a++){var u=n?a:i[a];l[u]=ts(e[u],t[u]),l[u]===e[u]&&o++}return r===s&&o===r?e:l}return t}function Wv(e,t){if(e&&!t||t&&!e)return!1;for(var n in e)if(e[n]!==t[n])return!1;return!0}function oo(e){if(!Bu(e))return!1;var t=e.constructor;if(typeof t>"u")return!0;var n=t.prototype;return!(!Bu(n)||!n.hasOwnProperty("isPrototypeOf"))}function Bu(e){return Object.prototype.toString.call(e)==="[object Object]"}function ks(e){return typeof e=="string"||Array.isArray(e)}function Kv(e){return new Promise(function(t){setTimeout(t,e)})}function Qu(e){Promise.resolve().then(e).catch(function(t){return setTimeout(function(){throw t})})}function Dd(){if(typeof AbortController=="function")return new AbortController}var Jv=function(e){zr(t,e);function t(){var r;return r=e.call(this)||this,r.setup=function(i){var s;if(!Yi&&((s=window)!=null&&s.addEventListener)){var l=function(){return i()};return window.addEventListener("visibilitychange",l,!1),window.addEventListener("focus",l,!1),function(){window.removeEventListener("visibilitychange",l),window.removeEventListener("focus",l)}}},r}var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){if(!this.hasListeners()){var i;(i=this.cleanup)==null||i.call(this),this.cleanup=void 0}},n.setEventListener=function(i){var s,l=this;this.setup=i,(s=this.cleanup)==null||s.call(this),this.cleanup=i(function(o){typeof o=="boolean"?l.setFocused(o):l.onFocus()})},n.setFocused=function(i){this.focused=i,i&&this.onFocus()},n.onFocus=function(){this.listeners.forEach(function(i){i()})},n.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},t}($r),cr=new Jv,Xv=function(e){zr(t,e);function t(){var r;return r=e.call(this)||this,r.setup=function(i){var s;if(!Yi&&((s=window)!=null&&s.addEventListener)){var l=function(){return i()};return window.addEventListener("online",l,!1),window.addEventListener("offline",l,!1),function(){window.removeEventListener("online",l),window.removeEventListener("offline",l)}}},r}var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){if(!this.hasListeners()){var i;(i=this.cleanup)==null||i.call(this),this.cleanup=void 0}},n.setEventListener=function(i){var s,l=this;this.setup=i,(s=this.cleanup)==null||s.call(this),this.cleanup=i(function(o){typeof o=="boolean"?l.setOnline(o):l.onOnline()})},n.setOnline=function(i){this.online=i,i&&this.onOnline()},n.onOnline=function(){this.listeners.forEach(function(i){i()})},n.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},t}($r),Ei=new Xv;function Gv(e){return Math.min(1e3*Math.pow(2,e),3e4)}function ns(e){return typeof(e==null?void 0:e.cancel)=="function"}var Md=function(t){this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent};function Ci(e){return e instanceof Md}var Ud=function(t){var n=this,r=!1,i,s,l,o;this.abort=t.abort,this.cancel=function(v){return i==null?void 0:i(v)},this.cancelRetry=function(){r=!0},this.continueRetry=function(){r=!1},this.continue=function(){return s==null?void 0:s()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(v,g){l=v,o=g});var a=function(g){n.isResolved||(n.isResolved=!0,t.onSuccess==null||t.onSuccess(g),s==null||s(),l(g))},u=function(g){n.isResolved||(n.isResolved=!0,t.onError==null||t.onError(g),s==null||s(),o(g))},f=function(){return new Promise(function(g){s=g,n.isPaused=!0,t.onPause==null||t.onPause()}).then(function(){s=void 0,n.isPaused=!1,t.onContinue==null||t.onContinue()})},d=function v(){if(!n.isResolved){var g;try{g=t.fn()}catch(y){g=Promise.reject(y)}i=function(x){if(!n.isResolved&&(u(new Md(x)),n.abort==null||n.abort(),ns(g)))try{g.cancel()}catch{}},n.isTransportCancelable=ns(g),Promise.resolve(g).then(a).catch(function(y){var x,S;if(!n.isResolved){var m=(x=t.retry)!=null?x:3,h=(S=t.retryDelay)!=null?S:Gv,p=typeof h=="function"?h(n.failureCount,y):h,E=m===!0||typeof m=="number"&&n.failureCount<m||typeof m=="function"&&m(n.failureCount,y);if(r||!E){u(y);return}n.failureCount++,t.onFail==null||t.onFail(n.failureCount,y),Kv(p).then(function(){if(!cr.isFocused()||!Ei.isOnline())return f()}).then(function(){r?u(y):v()})}})}};d()},Yv=function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(n){n()},this.batchNotifyFn=function(n){n()}}var t=e.prototype;return t.batch=function(r){var i;this.transactions++;try{i=r()}finally{this.transactions--,this.transactions||this.flush()}return i},t.schedule=function(r){var i=this;this.transactions?this.queue.push(r):Qu(function(){i.notifyFn(r)})},t.batchCalls=function(r){var i=this;return function(){for(var s=arguments.length,l=new Array(s),o=0;o<s;o++)l[o]=arguments[o];i.schedule(function(){r.apply(void 0,l)})}},t.flush=function(){var r=this,i=this.queue;this.queue=[],i.length&&Qu(function(){r.batchNotifyFn(function(){i.forEach(function(s){r.notifyFn(s)})})})},t.setNotifyFunction=function(r){this.notifyFn=r},t.setBatchNotifyFunction=function(r){this.batchNotifyFn=r},e}(),J=new Yv,Id=console;function rs(){return Id}function Zv(e){Id=e}var ey=function(){function e(n){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=n.defaultOptions,this.setOptions(n.options),this.observers=[],this.cache=n.cache,this.queryKey=n.queryKey,this.queryHash=n.queryHash,this.initialState=n.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=n.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(r){var i;this.options=U({},this.defaultOptions,r),this.meta=r==null?void 0:r.meta,this.cacheTime=Math.max(this.cacheTime||0,(i=this.options.cacheTime)!=null?i:5*60*1e3)},t.setDefaultOptions=function(r){this.defaultOptions=r},t.scheduleGc=function(){var r=this;this.clearGcTimeout(),lo(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){r.optionalRemove()},this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(r,i){var s,l,o=this.state.data,a=Hv(r,o);return(s=(l=this.options).isDataEqual)!=null&&s.call(l,o,a)?a=o:this.options.structuralSharing!==!1&&(a=ts(o,a)),this.dispatch({data:a,type:"success",dataUpdatedAt:i==null?void 0:i.updatedAt}),a},t.setState=function(r,i){this.dispatch({type:"setState",state:r,setStateOptions:i})},t.cancel=function(r){var i,s=this.promise;return(i=this.retryer)==null||i.cancel(r),s?s.then(fe).catch(fe):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some(function(r){return r.options.enabled!==!1})},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(r){return r.getCurrentResult().isStale})},t.isStaleByTime=function(r){return r===void 0&&(r=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!Ld(this.state.dataUpdatedAt,r)},t.onFocus=function(){var r,i=this.observers.find(function(s){return s.shouldFetchOnWindowFocus()});i&&i.refetch(),(r=this.retryer)==null||r.continue()},t.onOnline=function(){var r,i=this.observers.find(function(s){return s.shouldFetchOnReconnect()});i&&i.refetch(),(r=this.retryer)==null||r.continue()},t.addObserver=function(r){this.observers.indexOf(r)===-1&&(this.observers.push(r),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:r}))},t.removeObserver=function(r){this.observers.indexOf(r)!==-1&&(this.observers=this.observers.filter(function(i){return i!==r}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:r}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(r,i){var s=this,l,o,a;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(i!=null&&i.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var u;return(u=this.retryer)==null||u.continueRetry(),this.promise}}if(r&&this.setOptions(r),!this.options.queryFn){var f=this.observers.find(function(h){return h.options.queryFn});f&&this.setOptions(f.options)}var d=Zi(this.queryKey),v=Dd(),g={queryKey:d,pageParam:void 0,meta:this.meta};Object.defineProperty(g,"signal",{enumerable:!0,get:function(){if(v)return s.abortSignalConsumed=!0,v.signal}});var y=function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(g)):Promise.reject("Missing queryFn")},x={fetchOptions:i,options:this.options,queryKey:d,state:this.state,fetchFn:y,meta:this.meta};if((l=this.options.behavior)!=null&&l.onFetch){var S;(S=this.options.behavior)==null||S.onFetch(x)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((o=x.fetchOptions)==null?void 0:o.meta)){var m;this.dispatch({type:"fetch",meta:(m=x.fetchOptions)==null?void 0:m.meta})}return this.retryer=new Ud({fn:x.fetchFn,abort:v==null||(a=v.abort)==null?void 0:a.bind(v),onSuccess:function(p){s.setData(p),s.cache.config.onSuccess==null||s.cache.config.onSuccess(p,s),s.cacheTime===0&&s.optionalRemove()},onError:function(p){Ci(p)&&p.silent||s.dispatch({type:"error",error:p}),Ci(p)||(s.cache.config.onError==null||s.cache.config.onError(p,s),rs().error(p)),s.cacheTime===0&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:x.options.retry,retryDelay:x.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(r){var i=this;this.state=this.reducer(this.state,r),J.batch(function(){i.observers.forEach(function(s){s.onQueryUpdate(r)}),i.cache.notify({query:i,type:"queryUpdated",action:r})})},t.getDefaultState=function(r){var i=typeof r.initialData=="function"?r.initialData():r.initialData,s=typeof r.initialData<"u",l=s?typeof r.initialDataUpdatedAt=="function"?r.initialDataUpdatedAt():r.initialDataUpdatedAt:0,o=typeof i<"u";return{data:i,dataUpdateCount:0,dataUpdatedAt:o?l??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:o?"success":"idle"}},t.reducer=function(r,i){var s,l;switch(i.type){case"failed":return U({},r,{fetchFailureCount:r.fetchFailureCount+1});case"pause":return U({},r,{isPaused:!0});case"continue":return U({},r,{isPaused:!1});case"fetch":return U({},r,{fetchFailureCount:0,fetchMeta:(s=i.meta)!=null?s:null,isFetching:!0,isPaused:!1},!r.dataUpdatedAt&&{error:null,status:"loading"});case"success":return U({},r,{data:i.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:(l=i.dataUpdatedAt)!=null?l:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var o=i.error;return Ci(o)&&o.revert&&this.revertState?U({},this.revertState):U({},r,{error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return U({},r,{isInvalidated:!0});case"setState":return U({},r,i.state);default:return r}},e}(),ty=function(e){zr(t,e);function t(r){var i;return i=e.call(this)||this,i.config=r||{},i.queries=[],i.queriesMap={},i}var n=t.prototype;return n.build=function(i,s,l){var o,a=s.queryKey,u=(o=s.queryHash)!=null?o:pa(a,s),f=this.get(u);return f||(f=new ey({cache:this,queryKey:a,queryHash:u,options:i.defaultQueryOptions(s),state:l,defaultOptions:i.getQueryDefaults(a),meta:s.meta}),this.add(f)),f},n.add=function(i){this.queriesMap[i.queryHash]||(this.queriesMap[i.queryHash]=i,this.queries.push(i),this.notify({type:"queryAdded",query:i}))},n.remove=function(i){var s=this.queriesMap[i.queryHash];s&&(i.destroy(),this.queries=this.queries.filter(function(l){return l!==i}),s===i&&delete this.queriesMap[i.queryHash],this.notify({type:"queryRemoved",query:i}))},n.clear=function(){var i=this;J.batch(function(){i.queries.forEach(function(s){i.remove(s)})})},n.get=function(i){return this.queriesMap[i]},n.getAll=function(){return this.queries},n.find=function(i,s){var l=xt(i,s),o=l[0];return typeof o.exact>"u"&&(o.exact=!0),this.queries.find(function(a){return zu(o,a)})},n.findAll=function(i,s){var l=xt(i,s),o=l[0];return Object.keys(o).length>0?this.queries.filter(function(a){return zu(o,a)}):this.queries},n.notify=function(i){var s=this;J.batch(function(){s.listeners.forEach(function(l){l(i)})})},n.onFocus=function(){var i=this;J.batch(function(){i.queries.forEach(function(s){s.onFocus()})})},n.onOnline=function(){var i=this;J.batch(function(){i.queries.forEach(function(s){s.onOnline()})})},t}($r),ny=function(){function e(n){this.options=U({},n.defaultOptions,n.options),this.mutationId=n.mutationId,this.mutationCache=n.mutationCache,this.observers=[],this.state=n.state||ry(),this.meta=n.meta}var t=e.prototype;return t.setState=function(r){this.dispatch({type:"setState",state:r})},t.addObserver=function(r){this.observers.indexOf(r)===-1&&this.observers.push(r)},t.removeObserver=function(r){this.observers=this.observers.filter(function(i){return i!==r})},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(fe).catch(fe)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var r=this,i,s=this.state.status==="loading",l=Promise.resolve();return s||(this.dispatch({type:"loading",variables:this.options.variables}),l=l.then(function(){r.mutationCache.config.onMutate==null||r.mutationCache.config.onMutate(r.state.variables,r)}).then(function(){return r.options.onMutate==null?void 0:r.options.onMutate(r.state.variables)}).then(function(o){o!==r.state.context&&r.dispatch({type:"loading",context:o,variables:r.state.variables})})),l.then(function(){return r.executeMutation()}).then(function(o){i=o,r.mutationCache.config.onSuccess==null||r.mutationCache.config.onSuccess(i,r.state.variables,r.state.context,r)}).then(function(){return r.options.onSuccess==null?void 0:r.options.onSuccess(i,r.state.variables,r.state.context)}).then(function(){return r.options.onSettled==null?void 0:r.options.onSettled(i,null,r.state.variables,r.state.context)}).then(function(){return r.dispatch({type:"success",data:i}),i}).catch(function(o){return r.mutationCache.config.onError==null||r.mutationCache.config.onError(o,r.state.variables,r.state.context,r),rs().error(o),Promise.resolve().then(function(){return r.options.onError==null?void 0:r.options.onError(o,r.state.variables,r.state.context)}).then(function(){return r.options.onSettled==null?void 0:r.options.onSettled(void 0,o,r.state.variables,r.state.context)}).then(function(){throw r.dispatch({type:"error",error:o}),o})})},t.executeMutation=function(){var r=this,i;return this.retryer=new Ud({fn:function(){return r.options.mutationFn?r.options.mutationFn(r.state.variables):Promise.reject("No mutationFn found")},onFail:function(){r.dispatch({type:"failed"})},onPause:function(){r.dispatch({type:"pause"})},onContinue:function(){r.dispatch({type:"continue"})},retry:(i=this.options.retry)!=null?i:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(r){var i=this;this.state=iy(this.state,r),J.batch(function(){i.observers.forEach(function(s){s.onMutationUpdate(r)}),i.mutationCache.notify(i)})},e}();function ry(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function iy(e,t){switch(t.type){case"failed":return U({},e,{failureCount:e.failureCount+1});case"pause":return U({},e,{isPaused:!0});case"continue":return U({},e,{isPaused:!1});case"loading":return U({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return U({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return U({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return U({},e,t.state);default:return e}}var sy=function(e){zr(t,e);function t(r){var i;return i=e.call(this)||this,i.config=r||{},i.mutations=[],i.mutationId=0,i}var n=t.prototype;return n.build=function(i,s,l){var o=new ny({mutationCache:this,mutationId:++this.mutationId,options:i.defaultMutationOptions(s),state:l,defaultOptions:s.mutationKey?i.getMutationDefaults(s.mutationKey):void 0,meta:s.meta});return this.add(o),o},n.add=function(i){this.mutations.push(i),this.notify(i)},n.remove=function(i){this.mutations=this.mutations.filter(function(s){return s!==i}),i.cancel(),this.notify(i)},n.clear=function(){var i=this;J.batch(function(){i.mutations.forEach(function(s){i.remove(s)})})},n.getAll=function(){return this.mutations},n.find=function(i){return typeof i.exact>"u"&&(i.exact=!0),this.mutations.find(function(s){return $u(i,s)})},n.findAll=function(i){return this.mutations.filter(function(s){return $u(i,s)})},n.notify=function(i){var s=this;J.batch(function(){s.listeners.forEach(function(l){l(i)})})},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var i=this.mutations.filter(function(s){return s.state.isPaused});return J.batch(function(){return i.reduce(function(s,l){return s.then(function(){return l.continue().catch(fe)})},Promise.resolve())})},t}($r);function ly(){return{onFetch:function(t){t.fetchFn=function(){var n,r,i,s,l,o,a=(n=t.fetchOptions)==null||(r=n.meta)==null?void 0:r.refetchPage,u=(i=t.fetchOptions)==null||(s=i.meta)==null?void 0:s.fetchMore,f=u==null?void 0:u.pageParam,d=(u==null?void 0:u.direction)==="forward",v=(u==null?void 0:u.direction)==="backward",g=((l=t.state.data)==null?void 0:l.pages)||[],y=((o=t.state.data)==null?void 0:o.pageParams)||[],x=Dd(),S=x==null?void 0:x.signal,m=y,h=!1,p=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},E=function(be,mt,we,it){return m=it?[mt].concat(m):[].concat(m,[mt]),it?[we].concat(be):[].concat(be,[we])},C=function(be,mt,we,it){if(h)return Promise.reject("Cancelled");if(typeof we>"u"&&!mt&&be.length)return Promise.resolve(be);var R={queryKey:t.queryKey,signal:S,pageParam:we,meta:t.meta},T=p(R),L=Promise.resolve(T).then(function(Y){return E(be,we,Y,it)});if(ns(T)){var B=L;B.cancel=T.cancel}return L},N;if(!g.length)N=C([]);else if(d){var P=typeof f<"u",_=P?f:bu(t.options,g);N=C(g,P,_)}else if(v){var I=typeof f<"u",F=I?f:oy(t.options,g);N=C(g,I,F,!0)}else(function(){m=[];var _e=typeof t.options.getNextPageParam>"u",be=a&&g[0]?a(g[0],0,g):!0;N=be?C([],_e,y[0]):Promise.resolve(E([],y[0],g[0]));for(var mt=function(R){N=N.then(function(T){var L=a&&g[R]?a(g[R],R,g):!0;if(L){var B=_e?y[R]:bu(t.options,T);return C(T,_e,B)}return Promise.resolve(E(T,y[R],g[R]))})},we=1;we<g.length;we++)mt(we)})();var ae=N.then(function(_e){return{pages:_e,pageParams:m}}),Oe=ae;return Oe.cancel=function(){h=!0,x==null||x.abort(),ns(N)&&N.cancel()},ae}}}}function bu(e,t){return e.getNextPageParam==null?void 0:e.getNextPageParam(t[t.length-1],t)}function oy(e,t){return e.getPreviousPageParam==null?void 0:e.getPreviousPageParam(t[0],t)}var ay=function(){function e(n){n===void 0&&(n={}),this.queryCache=n.queryCache||new ty,this.mutationCache=n.mutationCache||new sy,this.defaultOptions=n.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var r=this;this.unsubscribeFocus=cr.subscribe(function(){cr.isFocused()&&Ei.isOnline()&&(r.mutationCache.onFocus(),r.queryCache.onFocus())}),this.unsubscribeOnline=Ei.subscribe(function(){cr.isFocused()&&Ei.isOnline()&&(r.mutationCache.onOnline(),r.queryCache.onOnline())})},t.unmount=function(){var r,i;(r=this.unsubscribeFocus)==null||r.call(this),(i=this.unsubscribeOnline)==null||i.call(this)},t.isFetching=function(r,i){var s=xt(r,i),l=s[0];return l.fetching=!0,this.queryCache.findAll(l).length},t.isMutating=function(r){return this.mutationCache.findAll(U({},r,{fetching:!0})).length},t.getQueryData=function(r,i){var s;return(s=this.queryCache.find(r,i))==null?void 0:s.state.data},t.getQueriesData=function(r){return this.getQueryCache().findAll(r).map(function(i){var s=i.queryKey,l=i.state,o=l.data;return[s,o]})},t.setQueryData=function(r,i,s){var l=Si(r),o=this.defaultQueryOptions(l);return this.queryCache.build(this,o).setData(i,s)},t.setQueriesData=function(r,i,s){var l=this;return J.batch(function(){return l.getQueryCache().findAll(r).map(function(o){var a=o.queryKey;return[a,l.setQueryData(a,i,s)]})})},t.getQueryState=function(r,i){var s;return(s=this.queryCache.find(r,i))==null?void 0:s.state},t.removeQueries=function(r,i){var s=xt(r,i),l=s[0],o=this.queryCache;J.batch(function(){o.findAll(l).forEach(function(a){o.remove(a)})})},t.resetQueries=function(r,i,s){var l=this,o=xt(r,i,s),a=o[0],u=o[1],f=this.queryCache,d=U({},a,{active:!0});return J.batch(function(){return f.findAll(a).forEach(function(v){v.reset()}),l.refetchQueries(d,u)})},t.cancelQueries=function(r,i,s){var l=this,o=xt(r,i,s),a=o[0],u=o[1],f=u===void 0?{}:u;typeof f.revert>"u"&&(f.revert=!0);var d=J.batch(function(){return l.queryCache.findAll(a).map(function(v){return v.cancel(f)})});return Promise.all(d).then(fe).catch(fe)},t.invalidateQueries=function(r,i,s){var l,o,a,u=this,f=xt(r,i,s),d=f[0],v=f[1],g=U({},d,{active:(l=(o=d.refetchActive)!=null?o:d.active)!=null?l:!0,inactive:(a=d.refetchInactive)!=null?a:!1});return J.batch(function(){return u.queryCache.findAll(d).forEach(function(y){y.invalidate()}),u.refetchQueries(g,v)})},t.refetchQueries=function(r,i,s){var l=this,o=xt(r,i,s),a=o[0],u=o[1],f=J.batch(function(){return l.queryCache.findAll(a).map(function(v){return v.fetch(void 0,U({},u,{meta:{refetchPage:a==null?void 0:a.refetchPage}}))})}),d=Promise.all(f).then(fe);return u!=null&&u.throwOnError||(d=d.catch(fe)),d},t.fetchQuery=function(r,i,s){var l=Si(r,i,s),o=this.defaultQueryOptions(l);typeof o.retry>"u"&&(o.retry=!1);var a=this.queryCache.build(this,o);return a.isStaleByTime(o.staleTime)?a.fetch(o):Promise.resolve(a.state.data)},t.prefetchQuery=function(r,i,s){return this.fetchQuery(r,i,s).then(fe).catch(fe)},t.fetchInfiniteQuery=function(r,i,s){var l=Si(r,i,s);return l.behavior=ly(),this.fetchQuery(l)},t.prefetchInfiniteQuery=function(r,i,s){return this.fetchInfiniteQuery(r,i,s).then(fe).catch(fe)},t.cancelMutations=function(){var r=this,i=J.batch(function(){return r.mutationCache.getAll().map(function(s){return s.cancel()})});return Promise.all(i).then(fe).catch(fe)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(r){return this.mutationCache.build(this,r).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(r){this.defaultOptions=r},t.setQueryDefaults=function(r,i){var s=this.queryDefaults.find(function(l){return Wt(r)===Wt(l.queryKey)});s?s.defaultOptions=i:this.queryDefaults.push({queryKey:r,defaultOptions:i})},t.getQueryDefaults=function(r){var i;return r?(i=this.queryDefaults.find(function(s){return es(r,s.queryKey)}))==null?void 0:i.defaultOptions:void 0},t.setMutationDefaults=function(r,i){var s=this.mutationDefaults.find(function(l){return Wt(r)===Wt(l.mutationKey)});s?s.defaultOptions=i:this.mutationDefaults.push({mutationKey:r,defaultOptions:i})},t.getMutationDefaults=function(r){var i;return r?(i=this.mutationDefaults.find(function(s){return es(r,s.mutationKey)}))==null?void 0:i.defaultOptions:void 0},t.defaultQueryOptions=function(r){if(r!=null&&r._defaulted)return r;var i=U({},this.defaultOptions.queries,this.getQueryDefaults(r==null?void 0:r.queryKey),r,{_defaulted:!0});return!i.queryHash&&i.queryKey&&(i.queryHash=pa(i.queryKey,i)),i},t.defaultQueryObserverOptions=function(r){return this.defaultQueryOptions(r)},t.defaultMutationOptions=function(r){return r!=null&&r._defaulted?r:U({},this.defaultOptions.mutations,this.getMutationDefaults(r==null?void 0:r.mutationKey),r,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}(),uy=function(e){zr(t,e);function t(r,i){var s;return s=e.call(this)||this,s.client=r,s.options=i,s.trackedProps=[],s.selectError=null,s.bindMethods(),s.setOptions(i),s}var n=t.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),Hu(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return ao(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return ao(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(i,s){var l=this.options,o=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(i),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=l.queryKey),this.updateQuery();var a=this.hasListeners();a&&qu(this.currentQuery,o,this.options,l)&&this.executeFetch(),this.updateResult(s),a&&(this.currentQuery!==o||this.options.enabled!==l.enabled||this.options.staleTime!==l.staleTime)&&this.updateStaleTimeout();var u=this.computeRefetchInterval();a&&(this.currentQuery!==o||this.options.enabled!==l.enabled||u!==this.currentRefetchInterval)&&this.updateRefetchInterval(u)},n.getOptimisticResult=function(i){var s=this.client.defaultQueryObserverOptions(i),l=this.client.getQueryCache().build(this.client,s);return this.createResult(l,s)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(i,s){var l=this,o={},a=function(f){l.trackedProps.includes(f)||l.trackedProps.push(f)};return Object.keys(i).forEach(function(u){Object.defineProperty(o,u,{configurable:!1,enumerable:!0,get:function(){return a(u),i[u]}})}),(s.useErrorBoundary||s.suspense)&&a("error"),o},n.getNextResult=function(i){var s=this;return new Promise(function(l,o){var a=s.subscribe(function(u){u.isFetching||(a(),u.isError&&(i!=null&&i.throwOnError)?o(u.error):l(u))})})},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(i){return this.fetch(U({},i,{meta:{refetchPage:i==null?void 0:i.refetchPage}}))},n.fetchOptimistic=function(i){var s=this,l=this.client.defaultQueryObserverOptions(i),o=this.client.getQueryCache().build(this.client,l);return o.fetch().then(function(){return s.createResult(o,l)})},n.fetch=function(i){var s=this;return this.executeFetch(i).then(function(){return s.updateResult(),s.currentResult})},n.executeFetch=function(i){this.updateQuery();var s=this.currentQuery.fetch(this.options,i);return i!=null&&i.throwOnError||(s=s.catch(fe)),s},n.updateStaleTimeout=function(){var i=this;if(this.clearStaleTimeout(),!(Yi||this.currentResult.isStale||!lo(this.options.staleTime))){var s=Ld(this.currentResult.dataUpdatedAt,this.options.staleTime),l=s+1;this.staleTimeoutId=setTimeout(function(){i.currentResult.isStale||i.updateResult()},l)}},n.computeRefetchInterval=function(){var i;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(i=this.options.refetchInterval)!=null?i:!1},n.updateRefetchInterval=function(i){var s=this;this.clearRefetchInterval(),this.currentRefetchInterval=i,!(Yi||this.options.enabled===!1||!lo(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(s.options.refetchIntervalInBackground||cr.isFocused())&&s.executeFetch()},this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(i,s){var l=this.currentQuery,o=this.options,a=this.currentResult,u=this.currentResultState,f=this.currentResultOptions,d=i!==l,v=d?i.state:this.currentQueryInitialState,g=d?this.currentResult:this.previousQueryResult,y=i.state,x=y.dataUpdatedAt,S=y.error,m=y.errorUpdatedAt,h=y.isFetching,p=y.status,E=!1,C=!1,N;if(s.optimisticResults){var P=this.hasListeners(),_=!P&&Hu(i,s),I=P&&qu(i,l,s,o);(_||I)&&(h=!0,x||(p="loading"))}if(s.keepPreviousData&&!y.dataUpdateCount&&(g!=null&&g.isSuccess)&&p!=="error")N=g.data,x=g.dataUpdatedAt,p=g.status,E=!0;else if(s.select&&typeof y.data<"u")if(a&&y.data===(u==null?void 0:u.data)&&s.select===this.selectFn)N=this.selectResult;else try{this.selectFn=s.select,N=s.select(y.data),s.structuralSharing!==!1&&(N=ts(a==null?void 0:a.data,N)),this.selectResult=N,this.selectError=null}catch(Oe){rs().error(Oe),this.selectError=Oe}else N=y.data;if(typeof s.placeholderData<"u"&&typeof N>"u"&&(p==="loading"||p==="idle")){var F;if(a!=null&&a.isPlaceholderData&&s.placeholderData===(f==null?void 0:f.placeholderData))F=a.data;else if(F=typeof s.placeholderData=="function"?s.placeholderData():s.placeholderData,s.select&&typeof F<"u")try{F=s.select(F),s.structuralSharing!==!1&&(F=ts(a==null?void 0:a.data,F)),this.selectError=null}catch(Oe){rs().error(Oe),this.selectError=Oe}typeof F<"u"&&(p="success",N=F,C=!0)}this.selectError&&(S=this.selectError,N=this.selectResult,m=Date.now(),p="error");var ae={status:p,isLoading:p==="loading",isSuccess:p==="success",isError:p==="error",isIdle:p==="idle",data:N,dataUpdatedAt:x,error:S,errorUpdatedAt:m,failureCount:y.fetchFailureCount,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>v.dataUpdateCount||y.errorUpdateCount>v.errorUpdateCount,isFetching:h,isRefetching:h&&p!=="loading",isLoadingError:p==="error"&&y.dataUpdatedAt===0,isPlaceholderData:C,isPreviousData:E,isRefetchError:p==="error"&&y.dataUpdatedAt!==0,isStale:ma(i,s),refetch:this.refetch,remove:this.remove};return ae},n.shouldNotifyListeners=function(i,s){if(!s)return!0;var l=this.options,o=l.notifyOnChangeProps,a=l.notifyOnChangePropsExclusions;if(!o&&!a||o==="tracked"&&!this.trackedProps.length)return!0;var u=o==="tracked"?this.trackedProps:o;return Object.keys(i).some(function(f){var d=f,v=i[d]!==s[d],g=u==null?void 0:u.some(function(x){return x===f}),y=a==null?void 0:a.some(function(x){return x===f});return v&&!y&&(!u||g)})},n.updateResult=function(i){var s=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!Wv(this.currentResult,s)){var l={cache:!0};(i==null?void 0:i.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,s)&&(l.listeners=!0),this.notify(U({},l,i))}},n.updateQuery=function(){var i=this.client.getQueryCache().build(this.client,this.options);if(i!==this.currentQuery){var s=this.currentQuery;this.currentQuery=i,this.currentQueryInitialState=i.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(s==null||s.removeObserver(this),i.addObserver(this))}},n.onQueryUpdate=function(i){var s={};i.type==="success"?s.onSuccess=!0:i.type==="error"&&!Ci(i.error)&&(s.onError=!0),this.updateResult(s),this.hasListeners()&&this.updateTimers()},n.notify=function(i){var s=this;J.batch(function(){i.onSuccess?(s.options.onSuccess==null||s.options.onSuccess(s.currentResult.data),s.options.onSettled==null||s.options.onSettled(s.currentResult.data,null)):i.onError&&(s.options.onError==null||s.options.onError(s.currentResult.error),s.options.onSettled==null||s.options.onSettled(void 0,s.currentResult.error)),i.listeners&&s.listeners.forEach(function(l){l(s.currentResult)}),i.cache&&s.client.getQueryCache().notify({query:s.currentQuery,type:"observerResultsUpdated"})})},t}($r);function cy(e,t){return t.enabled!==!1&&!e.state.dataUpdatedAt&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Hu(e,t){return cy(e,t)||e.state.dataUpdatedAt>0&&ao(e,t,t.refetchOnMount)}function ao(e,t,n){if(t.enabled!==!1){var r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&ma(e,t)}return!1}function qu(e,t,n,r){return n.enabled!==!1&&(e!==t||r.enabled===!1)&&(!n.suspense||e.state.status!=="error")&&ma(e,n)}function ma(e,t){return e.isStaleByTime(t.staleTime)}var fy=$m.unstable_batchedUpdates;J.setBatchNotifyFunction(fy);var dy=console;Zv(dy);var Vu=he.createContext(void 0),zd=he.createContext(!1);function $d(e){return e&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Vu),window.ReactQueryClientContext):Vu}var hy=function(){var t=he.useContext($d(he.useContext(zd)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},py=function(t){var n=t.client,r=t.contextSharing,i=r===void 0?!1:r,s=t.children;he.useEffect(function(){return n.mount(),function(){n.unmount()}},[n]);var l=$d(i);return he.createElement(zd.Provider,{value:i},he.createElement(l.Provider,{value:n},s))};function my(){var e=!1;return{clearReset:function(){e=!1},reset:function(){e=!0},isReset:function(){return e}}}var vy=he.createContext(my()),yy=function(){return he.useContext(vy)};function gy(e,t,n){return typeof t=="function"?t.apply(void 0,n):typeof t=="boolean"?t:!!e}function xy(e,t){var n=he.useRef(!1),r=he.useState(0),i=r[1],s=hy(),l=yy(),o=s.defaultQueryObserverOptions(e);o.optimisticResults=!0,o.onError&&(o.onError=J.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=J.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=J.batchCalls(o.onSettled)),o.suspense&&(typeof o.staleTime!="number"&&(o.staleTime=1e3),o.cacheTime===0&&(o.cacheTime=1)),(o.suspense||o.useErrorBoundary)&&(l.isReset()||(o.retryOnMount=!1));var a=he.useState(function(){return new t(s,o)}),u=a[0],f=u.getOptimisticResult(o);if(he.useEffect(function(){n.current=!0,l.clearReset();var d=u.subscribe(J.batchCalls(function(){n.current&&i(function(v){return v+1})}));return u.updateResult(),function(){n.current=!1,d()}},[l,u]),he.useEffect(function(){u.setOptions(o,{listeners:!1})},[o,u]),o.suspense&&f.isLoading)throw u.fetchOptimistic(o).then(function(d){var v=d.data;o.onSuccess==null||o.onSuccess(v),o.onSettled==null||o.onSettled(v,null)}).catch(function(d){l.clearReset(),o.onError==null||o.onError(d),o.onSettled==null||o.onSettled(void 0,d)});if(f.isError&&!l.isReset()&&!f.isFetching&&gy(o.suspense,o.useErrorBoundary,[f.error,u.getCurrentQuery()]))throw f.error;return o.notifyOnChangeProps==="tracked"&&(f=u.trackResult(f,o)),f}function va(e,t,n){var r=Si(e,t,n);return xy(r,uy)}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var wy={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sy=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),je=(e,t)=>{const n=j.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:o="",children:a,...u},f)=>j.createElement("svg",{ref:f,...wy,width:i,height:i,stroke:r,strokeWidth:l?Number(s)*24/Number(i):s,className:["lucide",`lucide-${Sy(e)}`,o].join(" "),...u},[...t.map(([d,v])=>j.createElement(d,v)),...Array.isArray(a)?a:[a]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ey=je("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cy=je("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ky=je("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ny=je("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uo=je("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bd=je("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ry=je("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fr=je("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Py=je("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jy=je("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oy=je("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const co=je("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const is=je("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _y=je("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Ty=()=>{const e=Ir(),t=n=>e.pathname===n;return c.jsx("nav",{className:"bg-white shadow-sm border-b border-gray-200",children:c.jsx("div",{className:"container mx-auto px-4",children:c.jsxs("div",{className:"flex items-center justify-between h-16",children:[c.jsxs("div",{className:"flex items-center space-x-8",children:[c.jsxs(Pn,{to:"/",className:"flex items-center space-x-2",children:[c.jsx(is,{className:"h-8 w-8 text-primary-600"}),c.jsx("span",{className:"text-xl font-bold text-gray-900",children:"Trading Bot"})]}),c.jsxs("div",{className:"flex space-x-6",children:[c.jsxs(Pn,{to:"/",className:`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${t("/")?"bg-primary-100 text-primary-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:[c.jsx(Ny,{className:"h-4 w-4"}),c.jsx("span",{children:"Dashboard"})]}),c.jsxs(Pn,{to:"/logs",className:`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${t("/logs")?"bg-primary-100 text-primary-700":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:[c.jsx(Bd,{className:"h-4 w-4"}),c.jsx("span",{children:"Logs"})]})]})]}),c.jsx("div",{className:"flex items-center space-x-4",children:c.jsx("div",{className:"text-sm text-gray-500",children:"Technical Analysis & Sentiment"})})]})})})};function Qd(e,t){return function(){return e.apply(t,arguments)}}const{toString:Fy}=Object.prototype,{getPrototypeOf:ya}=Object,{iterator:Ns,toStringTag:bd}=Symbol,Rs=(e=>t=>{const n=Fy.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Ge=e=>(e=e.toLowerCase(),t=>Rs(t)===e),Ps=e=>t=>typeof t===e,{isArray:zn}=Array,Tr=Ps("undefined");function Ly(e){return e!==null&&!Tr(e)&&e.constructor!==null&&!Tr(e.constructor)&&Re(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Hd=Ge("ArrayBuffer");function Ay(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Hd(e.buffer),t}const Dy=Ps("string"),Re=Ps("function"),qd=Ps("number"),js=e=>e!==null&&typeof e=="object",My=e=>e===!0||e===!1,ki=e=>{if(Rs(e)!=="object")return!1;const t=ya(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(bd in e)&&!(Ns in e)},Uy=Ge("Date"),Iy=Ge("File"),zy=Ge("Blob"),$y=Ge("FileList"),By=e=>js(e)&&Re(e.pipe),Qy=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Re(e.append)&&((t=Rs(e))==="formdata"||t==="object"&&Re(e.toString)&&e.toString()==="[object FormData]"))},by=Ge("URLSearchParams"),[Hy,qy,Vy,Wy]=["ReadableStream","Request","Response","Headers"].map(Ge),Ky=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Br(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,i;if(typeof e!="object"&&(e=[e]),zn(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),l=s.length;let o;for(r=0;r<l;r++)o=s[r],t.call(null,e[o],o,e)}}function Vd(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,i;for(;r-- >0;)if(i=n[r],t===i.toLowerCase())return i;return null}const Kt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Wd=e=>!Tr(e)&&e!==Kt;function fo(){const{caseless:e}=Wd(this)&&this||{},t={},n=(r,i)=>{const s=e&&Vd(t,i)||i;ki(t[s])&&ki(r)?t[s]=fo(t[s],r):ki(r)?t[s]=fo({},r):zn(r)?t[s]=r.slice():t[s]=r};for(let r=0,i=arguments.length;r<i;r++)arguments[r]&&Br(arguments[r],n);return t}const Jy=(e,t,n,{allOwnKeys:r}={})=>(Br(t,(i,s)=>{n&&Re(i)?e[s]=Qd(i,n):e[s]=i},{allOwnKeys:r}),e),Xy=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Gy=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Yy=(e,t,n,r)=>{let i,s,l;const o={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),s=i.length;s-- >0;)l=i[s],(!r||r(l,e,t))&&!o[l]&&(t[l]=e[l],o[l]=!0);e=n!==!1&&ya(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Zy=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},eg=e=>{if(!e)return null;if(zn(e))return e;let t=e.length;if(!qd(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},tg=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ya(Uint8Array)),ng=(e,t)=>{const r=(e&&e[Ns]).call(e);let i;for(;(i=r.next())&&!i.done;){const s=i.value;t.call(e,s[0],s[1])}},rg=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},ig=Ge("HTMLFormElement"),sg=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,i){return r.toUpperCase()+i}),Wu=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),lg=Ge("RegExp"),Kd=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Br(n,(i,s)=>{let l;(l=t(i,s,e))!==!1&&(r[s]=l||i)}),Object.defineProperties(e,r)},og=e=>{Kd(e,(t,n)=>{if(Re(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Re(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ag=(e,t)=>{const n={},r=i=>{i.forEach(s=>{n[s]=!0})};return zn(e)?r(e):r(String(e).split(t)),n},ug=()=>{},cg=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function fg(e){return!!(e&&Re(e.append)&&e[bd]==="FormData"&&e[Ns])}const dg=e=>{const t=new Array(10),n=(r,i)=>{if(js(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[i]=r;const s=zn(r)?[]:{};return Br(r,(l,o)=>{const a=n(l,i+1);!Tr(a)&&(s[o]=a)}),t[i]=void 0,s}}return r};return n(e,0)},hg=Ge("AsyncFunction"),pg=e=>e&&(js(e)||Re(e))&&Re(e.then)&&Re(e.catch),Jd=((e,t)=>e?setImmediate:t?((n,r)=>(Kt.addEventListener("message",({source:i,data:s})=>{i===Kt&&s===n&&r.length&&r.shift()()},!1),i=>{r.push(i),Kt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Re(Kt.postMessage)),mg=typeof queueMicrotask<"u"?queueMicrotask.bind(Kt):typeof process<"u"&&process.nextTick||Jd,vg=e=>e!=null&&Re(e[Ns]),w={isArray:zn,isArrayBuffer:Hd,isBuffer:Ly,isFormData:Qy,isArrayBufferView:Ay,isString:Dy,isNumber:qd,isBoolean:My,isObject:js,isPlainObject:ki,isReadableStream:Hy,isRequest:qy,isResponse:Vy,isHeaders:Wy,isUndefined:Tr,isDate:Uy,isFile:Iy,isBlob:zy,isRegExp:lg,isFunction:Re,isStream:By,isURLSearchParams:by,isTypedArray:tg,isFileList:$y,forEach:Br,merge:fo,extend:Jy,trim:Ky,stripBOM:Xy,inherits:Gy,toFlatObject:Yy,kindOf:Rs,kindOfTest:Ge,endsWith:Zy,toArray:eg,forEachEntry:ng,matchAll:rg,isHTMLForm:ig,hasOwnProperty:Wu,hasOwnProp:Wu,reduceDescriptors:Kd,freezeMethods:og,toObjectSet:ag,toCamelCase:sg,noop:ug,toFiniteNumber:cg,findKey:Vd,global:Kt,isContextDefined:Wd,isSpecCompliantForm:fg,toJSONObject:dg,isAsyncFn:hg,isThenable:pg,setImmediate:Jd,asap:mg,isIterable:vg};function A(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}w.inherits(A,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:w.toJSONObject(this.config),code:this.code,status:this.status}}});const Xd=A.prototype,Gd={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Gd[e]={value:e}});Object.defineProperties(A,Gd);Object.defineProperty(Xd,"isAxiosError",{value:!0});A.from=(e,t,n,r,i,s)=>{const l=Object.create(Xd);return w.toFlatObject(e,l,function(a){return a!==Error.prototype},o=>o!=="isAxiosError"),A.call(l,e.message,t,n,r,i),l.cause=e,l.name=e.name,s&&Object.assign(l,s),l};const yg=null;function ho(e){return w.isPlainObject(e)||w.isArray(e)}function Yd(e){return w.endsWith(e,"[]")?e.slice(0,-2):e}function Ku(e,t,n){return e?e.concat(t).map(function(i,s){return i=Yd(i),!n&&s?"["+i+"]":i}).join(n?".":""):t}function gg(e){return w.isArray(e)&&!e.some(ho)}const xg=w.toFlatObject(w,{},null,function(t){return/^is[A-Z]/.test(t)});function Os(e,t,n){if(!w.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=w.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(x,S){return!w.isUndefined(S[x])});const r=n.metaTokens,i=n.visitor||f,s=n.dots,l=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&w.isSpecCompliantForm(t);if(!w.isFunction(i))throw new TypeError("visitor must be a function");function u(y){if(y===null)return"";if(w.isDate(y))return y.toISOString();if(w.isBoolean(y))return y.toString();if(!a&&w.isBlob(y))throw new A("Blob is not supported. Use a Buffer instead.");return w.isArrayBuffer(y)||w.isTypedArray(y)?a&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function f(y,x,S){let m=y;if(y&&!S&&typeof y=="object"){if(w.endsWith(x,"{}"))x=r?x:x.slice(0,-2),y=JSON.stringify(y);else if(w.isArray(y)&&gg(y)||(w.isFileList(y)||w.endsWith(x,"[]"))&&(m=w.toArray(y)))return x=Yd(x),m.forEach(function(p,E){!(w.isUndefined(p)||p===null)&&t.append(l===!0?Ku([x],E,s):l===null?x:x+"[]",u(p))}),!1}return ho(y)?!0:(t.append(Ku(S,x,s),u(y)),!1)}const d=[],v=Object.assign(xg,{defaultVisitor:f,convertValue:u,isVisitable:ho});function g(y,x){if(!w.isUndefined(y)){if(d.indexOf(y)!==-1)throw Error("Circular reference detected in "+x.join("."));d.push(y),w.forEach(y,function(m,h){(!(w.isUndefined(m)||m===null)&&i.call(t,m,w.isString(h)?h.trim():h,x,v))===!0&&g(m,x?x.concat(h):[h])}),d.pop()}}if(!w.isObject(e))throw new TypeError("data must be an object");return g(e),t}function Ju(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function ga(e,t){this._pairs=[],e&&Os(e,this,t)}const Zd=ga.prototype;Zd.append=function(t,n){this._pairs.push([t,n])};Zd.toString=function(t){const n=t?function(r){return t.call(this,r,Ju)}:Ju;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function wg(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eh(e,t,n){if(!t)return e;const r=n&&n.encode||wg;w.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let s;if(i?s=i(t,n):s=w.isURLSearchParams(t)?t.toString():new ga(t,n).toString(r),s){const l=e.indexOf("#");l!==-1&&(e=e.slice(0,l)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Xu{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){w.forEach(this.handlers,function(r){r!==null&&t(r)})}}const th={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Sg=typeof URLSearchParams<"u"?URLSearchParams:ga,Eg=typeof FormData<"u"?FormData:null,Cg=typeof Blob<"u"?Blob:null,kg={isBrowser:!0,classes:{URLSearchParams:Sg,FormData:Eg,Blob:Cg},protocols:["http","https","file","blob","url","data"]},xa=typeof window<"u"&&typeof document<"u",po=typeof navigator=="object"&&navigator||void 0,Ng=xa&&(!po||["ReactNative","NativeScript","NS"].indexOf(po.product)<0),Rg=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Pg=xa&&window.location.href||"http://localhost",jg=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:xa,hasStandardBrowserEnv:Ng,hasStandardBrowserWebWorkerEnv:Rg,navigator:po,origin:Pg},Symbol.toStringTag,{value:"Module"})),pe={...jg,...kg};function Og(e,t){return Os(e,new pe.classes.URLSearchParams,Object.assign({visitor:function(n,r,i,s){return pe.isNode&&w.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function _g(e){return w.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Tg(e){const t={},n=Object.keys(e);let r;const i=n.length;let s;for(r=0;r<i;r++)s=n[r],t[s]=e[s];return t}function nh(e){function t(n,r,i,s){let l=n[s++];if(l==="__proto__")return!0;const o=Number.isFinite(+l),a=s>=n.length;return l=!l&&w.isArray(i)?i.length:l,a?(w.hasOwnProp(i,l)?i[l]=[i[l],r]:i[l]=r,!o):((!i[l]||!w.isObject(i[l]))&&(i[l]=[]),t(n,r,i[l],s)&&w.isArray(i[l])&&(i[l]=Tg(i[l])),!o)}if(w.isFormData(e)&&w.isFunction(e.entries)){const n={};return w.forEachEntry(e,(r,i)=>{t(_g(r),i,n,0)}),n}return null}function Fg(e,t,n){if(w.isString(e))try{return(t||JSON.parse)(e),w.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Qr={transitional:th,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",i=r.indexOf("application/json")>-1,s=w.isObject(t);if(s&&w.isHTMLForm(t)&&(t=new FormData(t)),w.isFormData(t))return i?JSON.stringify(nh(t)):t;if(w.isArrayBuffer(t)||w.isBuffer(t)||w.isStream(t)||w.isFile(t)||w.isBlob(t)||w.isReadableStream(t))return t;if(w.isArrayBufferView(t))return t.buffer;if(w.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Og(t,this.formSerializer).toString();if((o=w.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return Os(o?{"files[]":t}:t,a&&new a,this.formSerializer)}}return s||i?(n.setContentType("application/json",!1),Fg(t)):t}],transformResponse:[function(t){const n=this.transitional||Qr.transitional,r=n&&n.forcedJSONParsing,i=this.responseType==="json";if(w.isResponse(t)||w.isReadableStream(t))return t;if(t&&w.isString(t)&&(r&&!this.responseType||i)){const l=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(o){if(l)throw o.name==="SyntaxError"?A.from(o,A.ERR_BAD_RESPONSE,this,null,this.response):o}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:pe.classes.FormData,Blob:pe.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};w.forEach(["delete","get","head","post","put","patch"],e=>{Qr.headers[e]={}});const Lg=w.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ag=e=>{const t={};let n,r,i;return e&&e.split(`
`).forEach(function(l){i=l.indexOf(":"),n=l.substring(0,i).trim().toLowerCase(),r=l.substring(i+1).trim(),!(!n||t[n]&&Lg[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Gu=Symbol("internals");function Jn(e){return e&&String(e).trim().toLowerCase()}function Ni(e){return e===!1||e==null?e:w.isArray(e)?e.map(Ni):String(e)}function Dg(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Mg=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ll(e,t,n,r,i){if(w.isFunction(r))return r.call(this,t,n);if(i&&(t=n),!!w.isString(t)){if(w.isString(r))return t.indexOf(r)!==-1;if(w.isRegExp(r))return r.test(t)}}function Ug(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Ig(e,t){const n=w.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(i,s,l){return this[r].call(this,t,i,s,l)},configurable:!0})})}let Pe=class{constructor(t){t&&this.set(t)}set(t,n,r){const i=this;function s(o,a,u){const f=Jn(a);if(!f)throw new Error("header name must be a non-empty string");const d=w.findKey(i,f);(!d||i[d]===void 0||u===!0||u===void 0&&i[d]!==!1)&&(i[d||a]=Ni(o))}const l=(o,a)=>w.forEach(o,(u,f)=>s(u,f,a));if(w.isPlainObject(t)||t instanceof this.constructor)l(t,n);else if(w.isString(t)&&(t=t.trim())&&!Mg(t))l(Ag(t),n);else if(w.isObject(t)&&w.isIterable(t)){let o={},a,u;for(const f of t){if(!w.isArray(f))throw TypeError("Object iterator must return a key-value pair");o[u=f[0]]=(a=o[u])?w.isArray(a)?[...a,f[1]]:[a,f[1]]:f[1]}l(o,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=Jn(t),t){const r=w.findKey(this,t);if(r){const i=this[r];if(!n)return i;if(n===!0)return Dg(i);if(w.isFunction(n))return n.call(this,i,r);if(w.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Jn(t),t){const r=w.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ll(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let i=!1;function s(l){if(l=Jn(l),l){const o=w.findKey(r,l);o&&(!n||ll(r,r[o],o,n))&&(delete r[o],i=!0)}}return w.isArray(t)?t.forEach(s):s(t),i}clear(t){const n=Object.keys(this);let r=n.length,i=!1;for(;r--;){const s=n[r];(!t||ll(this,this[s],s,t,!0))&&(delete this[s],i=!0)}return i}normalize(t){const n=this,r={};return w.forEach(this,(i,s)=>{const l=w.findKey(r,s);if(l){n[l]=Ni(i),delete n[s];return}const o=t?Ug(s):String(s).trim();o!==s&&delete n[s],n[o]=Ni(i),r[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return w.forEach(this,(r,i)=>{r!=null&&r!==!1&&(n[i]=t&&w.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(i=>r.set(i)),r}static accessor(t){const r=(this[Gu]=this[Gu]={accessors:{}}).accessors,i=this.prototype;function s(l){const o=Jn(l);r[o]||(Ig(i,l),r[o]=!0)}return w.isArray(t)?t.forEach(s):s(t),this}};Pe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);w.reduceDescriptors(Pe.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});w.freezeMethods(Pe);function ol(e,t){const n=this||Qr,r=t||n,i=Pe.from(r.headers);let s=r.data;return w.forEach(e,function(o){s=o.call(n,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function rh(e){return!!(e&&e.__CANCEL__)}function $n(e,t,n){A.call(this,e??"canceled",A.ERR_CANCELED,t,n),this.name="CanceledError"}w.inherits($n,A,{__CANCEL__:!0});function ih(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new A("Request failed with status code "+n.status,[A.ERR_BAD_REQUEST,A.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function zg(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function $g(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i=0,s=0,l;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),f=r[s];l||(l=u),n[i]=a,r[i]=u;let d=s,v=0;for(;d!==i;)v+=n[d++],d=d%e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),u-l<t)return;const g=f&&u-f;return g?Math.round(v*1e3/g):void 0}}function Bg(e,t){let n=0,r=1e3/t,i,s;const l=(u,f=Date.now())=>{n=f,i=null,s&&(clearTimeout(s),s=null),e.apply(null,u)};return[(...u)=>{const f=Date.now(),d=f-n;d>=r?l(u,f):(i=u,s||(s=setTimeout(()=>{s=null,l(i)},r-d)))},()=>i&&l(i)]}const ss=(e,t,n=3)=>{let r=0;const i=$g(50,250);return Bg(s=>{const l=s.loaded,o=s.lengthComputable?s.total:void 0,a=l-r,u=i(a),f=l<=o;r=l;const d={loaded:l,total:o,progress:o?l/o:void 0,bytes:a,rate:u||void 0,estimated:u&&o&&f?(o-l)/u:void 0,event:s,lengthComputable:o!=null,[t?"download":"upload"]:!0};e(d)},n)},Yu=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Zu=e=>(...t)=>w.asap(()=>e(...t)),Qg=pe.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,pe.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(pe.origin),pe.navigator&&/(msie|trident)/i.test(pe.navigator.userAgent)):()=>!0,bg=pe.hasStandardBrowserEnv?{write(e,t,n,r,i,s){const l=[e+"="+encodeURIComponent(t)];w.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),w.isString(r)&&l.push("path="+r),w.isString(i)&&l.push("domain="+i),s===!0&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Hg(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function qg(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function sh(e,t,n){let r=!Hg(t);return e&&(r||n==!1)?qg(e,t):t}const ec=e=>e instanceof Pe?{...e}:e;function rn(e,t){t=t||{};const n={};function r(u,f,d,v){return w.isPlainObject(u)&&w.isPlainObject(f)?w.merge.call({caseless:v},u,f):w.isPlainObject(f)?w.merge({},f):w.isArray(f)?f.slice():f}function i(u,f,d,v){if(w.isUndefined(f)){if(!w.isUndefined(u))return r(void 0,u,d,v)}else return r(u,f,d,v)}function s(u,f){if(!w.isUndefined(f))return r(void 0,f)}function l(u,f){if(w.isUndefined(f)){if(!w.isUndefined(u))return r(void 0,u)}else return r(void 0,f)}function o(u,f,d){if(d in t)return r(u,f);if(d in e)return r(void 0,u)}const a={url:s,method:s,data:s,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:o,headers:(u,f,d)=>i(ec(u),ec(f),d,!0)};return w.forEach(Object.keys(Object.assign({},e,t)),function(f){const d=a[f]||i,v=d(e[f],t[f],f);w.isUndefined(v)&&d!==o||(n[f]=v)}),n}const lh=e=>{const t=rn({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:s,headers:l,auth:o}=t;t.headers=l=Pe.from(l),t.url=eh(sh(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),o&&l.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):"")));let a;if(w.isFormData(n)){if(pe.hasStandardBrowserEnv||pe.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if((a=l.getContentType())!==!1){const[u,...f]=a?a.split(";").map(d=>d.trim()).filter(Boolean):[];l.setContentType([u||"multipart/form-data",...f].join("; "))}}if(pe.hasStandardBrowserEnv&&(r&&w.isFunction(r)&&(r=r(t)),r||r!==!1&&Qg(t.url))){const u=i&&s&&bg.read(s);u&&l.set(i,u)}return t},Vg=typeof XMLHttpRequest<"u",Wg=Vg&&function(e){return new Promise(function(n,r){const i=lh(e);let s=i.data;const l=Pe.from(i.headers).normalize();let{responseType:o,onUploadProgress:a,onDownloadProgress:u}=i,f,d,v,g,y;function x(){g&&g(),y&&y(),i.cancelToken&&i.cancelToken.unsubscribe(f),i.signal&&i.signal.removeEventListener("abort",f)}let S=new XMLHttpRequest;S.open(i.method.toUpperCase(),i.url,!0),S.timeout=i.timeout;function m(){if(!S)return;const p=Pe.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),C={data:!o||o==="text"||o==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:p,config:e,request:S};ih(function(P){n(P),x()},function(P){r(P),x()},C),S=null}"onloadend"in S?S.onloadend=m:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(m)},S.onabort=function(){S&&(r(new A("Request aborted",A.ECONNABORTED,e,S)),S=null)},S.onerror=function(){r(new A("Network Error",A.ERR_NETWORK,e,S)),S=null},S.ontimeout=function(){let E=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const C=i.transitional||th;i.timeoutErrorMessage&&(E=i.timeoutErrorMessage),r(new A(E,C.clarifyTimeoutError?A.ETIMEDOUT:A.ECONNABORTED,e,S)),S=null},s===void 0&&l.setContentType(null),"setRequestHeader"in S&&w.forEach(l.toJSON(),function(E,C){S.setRequestHeader(C,E)}),w.isUndefined(i.withCredentials)||(S.withCredentials=!!i.withCredentials),o&&o!=="json"&&(S.responseType=i.responseType),u&&([v,y]=ss(u,!0),S.addEventListener("progress",v)),a&&S.upload&&([d,g]=ss(a),S.upload.addEventListener("progress",d),S.upload.addEventListener("loadend",g)),(i.cancelToken||i.signal)&&(f=p=>{S&&(r(!p||p.type?new $n(null,e,S):p),S.abort(),S=null)},i.cancelToken&&i.cancelToken.subscribe(f),i.signal&&(i.signal.aborted?f():i.signal.addEventListener("abort",f)));const h=zg(i.url);if(h&&pe.protocols.indexOf(h)===-1){r(new A("Unsupported protocol "+h+":",A.ERR_BAD_REQUEST,e));return}S.send(s||null)})},Kg=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,i;const s=function(u){if(!i){i=!0,o();const f=u instanceof Error?u:this.reason;r.abort(f instanceof A?f:new $n(f instanceof Error?f.message:f))}};let l=t&&setTimeout(()=>{l=null,s(new A(`timeout ${t} of ms exceeded`,A.ETIMEDOUT))},t);const o=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(s):u.removeEventListener("abort",s)}),e=null)};e.forEach(u=>u.addEventListener("abort",s));const{signal:a}=r;return a.unsubscribe=()=>w.asap(o),a}},Jg=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,i;for(;r<n;)i=r+t,yield e.slice(r,i),r=i},Xg=async function*(e,t){for await(const n of Gg(e))yield*Jg(n,t)},Gg=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},tc=(e,t,n,r)=>{const i=Xg(e,t);let s=0,l,o=a=>{l||(l=!0,r&&r(a))};return new ReadableStream({async pull(a){try{const{done:u,value:f}=await i.next();if(u){o(),a.close();return}let d=f.byteLength;if(n){let v=s+=d;n(v)}a.enqueue(new Uint8Array(f))}catch(u){throw o(u),u}},cancel(a){return o(a),i.return()}},{highWaterMark:2})},_s=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",oh=_s&&typeof ReadableStream=="function",Yg=_s&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ah=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Zg=oh&&ah(()=>{let e=!1;const t=new Request(pe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),nc=64*1024,mo=oh&&ah(()=>w.isReadableStream(new Response("").body)),ls={stream:mo&&(e=>e.body)};_s&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ls[t]&&(ls[t]=w.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new A(`Response type '${t}' is not supported`,A.ERR_NOT_SUPPORT,r)})})})(new Response);const e0=async e=>{if(e==null)return 0;if(w.isBlob(e))return e.size;if(w.isSpecCompliantForm(e))return(await new Request(pe.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(w.isArrayBufferView(e)||w.isArrayBuffer(e))return e.byteLength;if(w.isURLSearchParams(e)&&(e=e+""),w.isString(e))return(await Yg(e)).byteLength},t0=async(e,t)=>{const n=w.toFiniteNumber(e.getContentLength());return n??e0(t)},n0=_s&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:s,timeout:l,onDownloadProgress:o,onUploadProgress:a,responseType:u,headers:f,withCredentials:d="same-origin",fetchOptions:v}=lh(e);u=u?(u+"").toLowerCase():"text";let g=Kg([i,s&&s.toAbortSignal()],l),y;const x=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let S;try{if(a&&Zg&&n!=="get"&&n!=="head"&&(S=await t0(f,r))!==0){let C=new Request(t,{method:"POST",body:r,duplex:"half"}),N;if(w.isFormData(r)&&(N=C.headers.get("content-type"))&&f.setContentType(N),C.body){const[P,_]=Yu(S,ss(Zu(a)));r=tc(C.body,nc,P,_)}}w.isString(d)||(d=d?"include":"omit");const m="credentials"in Request.prototype;y=new Request(t,{...v,signal:g,method:n.toUpperCase(),headers:f.normalize().toJSON(),body:r,duplex:"half",credentials:m?d:void 0});let h=await fetch(y,v);const p=mo&&(u==="stream"||u==="response");if(mo&&(o||p&&x)){const C={};["status","statusText","headers"].forEach(I=>{C[I]=h[I]});const N=w.toFiniteNumber(h.headers.get("content-length")),[P,_]=o&&Yu(N,ss(Zu(o),!0))||[];h=new Response(tc(h.body,nc,P,()=>{_&&_(),x&&x()}),C)}u=u||"text";let E=await ls[w.findKey(ls,u)||"text"](h,e);return!p&&x&&x(),await new Promise((C,N)=>{ih(C,N,{data:E,headers:Pe.from(h.headers),status:h.status,statusText:h.statusText,config:e,request:y})})}catch(m){throw x&&x(),m&&m.name==="TypeError"&&/Load failed|fetch/i.test(m.message)?Object.assign(new A("Network Error",A.ERR_NETWORK,e,y),{cause:m.cause||m}):A.from(m,m&&m.code,e,y)}}),vo={http:yg,xhr:Wg,fetch:n0};w.forEach(vo,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const rc=e=>`- ${e}`,r0=e=>w.isFunction(e)||e===null||e===!1,uh={getAdapter:e=>{e=w.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let s=0;s<t;s++){n=e[s];let l;if(r=n,!r0(n)&&(r=vo[(l=String(n)).toLowerCase()],r===void 0))throw new A(`Unknown adapter '${l}'`);if(r)break;i[l||"#"+s]=r}if(!r){const s=Object.entries(i).map(([o,a])=>`adapter ${o} `+(a===!1?"is not supported by the environment":"is not available in the build"));let l=t?s.length>1?`since :
`+s.map(rc).join(`
`):" "+rc(s[0]):"as no adapter specified";throw new A("There is no suitable adapter to dispatch the request "+l,"ERR_NOT_SUPPORT")}return r},adapters:vo};function al(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new $n(null,e)}function ic(e){return al(e),e.headers=Pe.from(e.headers),e.data=ol.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),uh.getAdapter(e.adapter||Qr.adapter)(e).then(function(r){return al(e),r.data=ol.call(e,e.transformResponse,r),r.headers=Pe.from(r.headers),r},function(r){return rh(r)||(al(e),r&&r.response&&(r.response.data=ol.call(e,e.transformResponse,r.response),r.response.headers=Pe.from(r.response.headers))),Promise.reject(r)})}const ch="1.10.0",Ts={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ts[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const sc={};Ts.transitional=function(t,n,r){function i(s,l){return"[Axios v"+ch+"] Transitional option '"+s+"'"+l+(r?". "+r:"")}return(s,l,o)=>{if(t===!1)throw new A(i(l," has been removed"+(n?" in "+n:"")),A.ERR_DEPRECATED);return n&&!sc[l]&&(sc[l]=!0,console.warn(i(l," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,l,o):!0}};Ts.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function i0(e,t,n){if(typeof e!="object")throw new A("options must be an object",A.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const s=r[i],l=t[s];if(l){const o=e[s],a=o===void 0||l(o,s,e);if(a!==!0)throw new A("option "+s+" must be "+a,A.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new A("Unknown option "+s,A.ERR_BAD_OPTION)}}const Ri={assertOptions:i0,validators:Ts},Ze=Ri.validators;let Gt=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Xu,response:new Xu}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const s=i.stack?i.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=rn(this.defaults,n);const{transitional:r,paramsSerializer:i,headers:s}=n;r!==void 0&&Ri.assertOptions(r,{silentJSONParsing:Ze.transitional(Ze.boolean),forcedJSONParsing:Ze.transitional(Ze.boolean),clarifyTimeoutError:Ze.transitional(Ze.boolean)},!1),i!=null&&(w.isFunction(i)?n.paramsSerializer={serialize:i}:Ri.assertOptions(i,{encode:Ze.function,serialize:Ze.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Ri.assertOptions(n,{baseUrl:Ze.spelling("baseURL"),withXsrfToken:Ze.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let l=s&&w.merge(s.common,s[n.method]);s&&w.forEach(["delete","get","head","post","put","patch","common"],y=>{delete s[y]}),n.headers=Pe.concat(l,s);const o=[];let a=!0;this.interceptors.request.forEach(function(x){typeof x.runWhen=="function"&&x.runWhen(n)===!1||(a=a&&x.synchronous,o.unshift(x.fulfilled,x.rejected))});const u=[];this.interceptors.response.forEach(function(x){u.push(x.fulfilled,x.rejected)});let f,d=0,v;if(!a){const y=[ic.bind(this),void 0];for(y.unshift.apply(y,o),y.push.apply(y,u),v=y.length,f=Promise.resolve(n);d<v;)f=f.then(y[d++],y[d++]);return f}v=o.length;let g=n;for(d=0;d<v;){const y=o[d++],x=o[d++];try{g=y(g)}catch(S){x.call(this,S);break}}try{f=ic.call(this,g)}catch(y){return Promise.reject(y)}for(d=0,v=u.length;d<v;)f=f.then(u[d++],u[d++]);return f}getUri(t){t=rn(this.defaults,t);const n=sh(t.baseURL,t.url,t.allowAbsoluteUrls);return eh(n,t.params,t.paramsSerializer)}};w.forEach(["delete","get","head","options"],function(t){Gt.prototype[t]=function(n,r){return this.request(rn(r||{},{method:t,url:n,data:(r||{}).data}))}});w.forEach(["post","put","patch"],function(t){function n(r){return function(s,l,o){return this.request(rn(o||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:l}))}}Gt.prototype[t]=n(),Gt.prototype[t+"Form"]=n(!0)});let s0=class fh{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(i=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](i);r._listeners=null}),this.promise.then=i=>{let s;const l=new Promise(o=>{r.subscribe(o),s=o}).then(i);return l.cancel=function(){r.unsubscribe(s)},l},t(function(s,l,o){r.reason||(r.reason=new $n(s,l,o),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new fh(function(i){t=i}),cancel:t}}};function l0(e){return function(n){return e.apply(null,n)}}function o0(e){return w.isObject(e)&&e.isAxiosError===!0}const yo={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(yo).forEach(([e,t])=>{yo[t]=e});function dh(e){const t=new Gt(e),n=Qd(Gt.prototype.request,t);return w.extend(n,Gt.prototype,t,{allOwnKeys:!0}),w.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return dh(rn(e,i))},n}const G=dh(Qr);G.Axios=Gt;G.CanceledError=$n;G.CancelToken=s0;G.isCancel=rh;G.VERSION=ch;G.toFormData=Os;G.AxiosError=A;G.Cancel=G.CanceledError;G.all=function(t){return Promise.all(t)};G.spread=l0;G.isAxiosError=o0;G.mergeConfig=rn;G.AxiosHeaders=Pe;G.formToJSON=e=>nh(w.isHTMLForm(e)?new FormData(e):e);G.getAdapter=uh.getAdapter;G.HttpStatusCode=yo;G.default=G;const{Axios:y0,AxiosError:g0,CanceledError:x0,isCancel:w0,CancelToken:S0,VERSION:E0,all:C0,Cancel:k0,isAxiosError:N0,spread:R0,toFormData:P0,AxiosHeaders:j0,HttpStatusCode:O0,formToJSON:_0,getAdapter:T0,mergeConfig:F0}=G,a0="/api",er=G.create({baseURL:a0,timeout:1e4});er.interceptors.response.use(e=>e,e=>(console.error("API Error:",e),Promise.reject(e)));const wa={getAll:async()=>(await er.get("/tickers")).data.data||[],getByTicker:async e=>{const t=await er.get(`/tickers/${e}`);if(!t.data.success||!t.data.data)throw new Error(t.data.error||"Failed to fetch ticker data");return t.data.data},getDashboardSummary:async()=>{const e=await er.get("/tickers/dashboard/summary");if(!e.data.success||!e.data.data)throw new Error(e.data.error||"Failed to fetch dashboard data");return e.data.data},getLogs:async e=>(await er.get("/tickers/logs",{params:e})).data.data||[]},u0=()=>{const[e,t]=j.useState([]);j.useEffect(()=>{const i=s=>{const l={...s.detail,id:Date.now().toString()};t(o=>[...o,l]),setTimeout(()=>{t(o=>o.filter(a=>a.id!==l.id))},5e3)};return window.addEventListener("show-toast",i),()=>window.removeEventListener("show-toast",i)},[]);const n=i=>{t(s=>s.filter(l=>l.id!==i))},r=i=>{switch(i){case"success":return"bg-success-50 border-success-200 text-success-800";case"error":return"bg-danger-50 border-danger-200 text-danger-800";case"info":return"bg-primary-50 border-primary-200 text-primary-800";default:return"bg-gray-50 border-gray-200 text-gray-800"}};return c.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:e.map(i=>c.jsxs("div",{className:`flex items-center justify-between p-4 rounded-lg border shadow-lg max-w-sm ${r(i.type)}`,children:[c.jsx("span",{className:"text-sm font-medium",children:i.message}),c.jsx("button",{onClick:()=>n(i.id),className:"ml-4 text-gray-400 hover:text-gray-600",children:c.jsx(_y,{className:"h-4 w-4"})})]},i.id))})},Sa=(e,t="info")=>{window.dispatchEvent(new CustomEvent("show-toast",{detail:{message:e,type:t}}))},c0=()=>{const{data:e,isLoading:t,error:n}=va("dashboard",wa.getDashboardSummary,{refetchInterval:3e4,onError:()=>Sa("Failed to load dashboard data","error")});if(t)return c.jsx("div",{className:"flex items-center justify-center h-64",children:c.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"})});if(n)return c.jsxs("div",{className:"text-center py-12",children:[c.jsx("div",{className:"text-red-600 text-lg font-medium",children:"Failed to load dashboard"}),c.jsx("div",{className:"text-gray-600 mt-2",children:"Please try again later"})]});if(!e)return c.jsx("div",{className:"text-center py-12",children:c.jsx("div",{className:"text-gray-600",children:"No data available"})});const{tickers:r,summary:i}=e,s=a=>{switch(a){case"Buy":return c.jsx(is,{className:"h-5 w-5 text-success-600"});case"Sell":return c.jsx(co,{className:"h-5 w-5 text-danger-600"});case"Hold":return c.jsx(fr,{className:"h-5 w-5 text-warning-600"});default:return c.jsx(fr,{className:"h-5 w-5 text-gray-600"})}},l=a=>{switch(a){case"Buy":return"badge-success";case"Sell":return"badge-danger";case"Hold":return"badge-warning";default:return"badge-warning"}},o=a=>{switch(a){case"Positive":return"badge-success";case"Negative":return"badge-danger";case"Neutral":return"badge-warning";default:return"badge-warning"}};return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Trading Dashboard"}),c.jsxs("p",{className:"text-gray-600 mt-1",children:["Technical Analysis & News Sentiment for ",i.totalTickers," Tickers"]})]}),c.jsxs("div",{className:"text-sm text-gray-500",children:["Last updated: ",new Date(e.lastUpdate).toLocaleString()]})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[c.jsx("div",{className:"card",children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 bg-success-100 rounded-lg",children:c.jsx(is,{className:"h-6 w-6 text-success-600"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Buy Signals"}),c.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.buySignals})]})]})}),c.jsx("div",{className:"card",children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 bg-danger-100 rounded-lg",children:c.jsx(co,{className:"h-6 w-6 text-danger-600"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Sell Signals"}),c.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.sellSignals})]})]})}),c.jsx("div",{className:"card",children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 bg-warning-100 rounded-lg",children:c.jsx(fr,{className:"h-6 w-6 text-warning-600"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Hold Signals"}),c.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.holdSignals})]})]})}),c.jsx("div",{className:"card",children:c.jsxs("div",{className:"flex items-center",children:[c.jsx("div",{className:"p-2 bg-primary-100 rounded-lg",children:c.jsx(Ey,{className:"h-6 w-6 text-primary-600"})}),c.jsxs("div",{className:"ml-4",children:[c.jsx("p",{className:"text-sm font-medium text-gray-600",children:"Avg Sentiment"}),c.jsx("p",{className:"text-2xl font-bold text-gray-900",children:i.averageSentiment})]})]})})]}),c.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:r.map(a=>c.jsxs(Pn,{to:`/ticker/${a.ticker}`,className:"card hover:shadow-md transition-shadow cursor-pointer",children:[c.jsxs("div",{className:"flex items-center justify-between mb-4",children:[c.jsxs("div",{children:[c.jsx("h3",{className:"text-xl font-bold text-gray-900",children:a.ticker}),c.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[c.jsx(uo,{className:"h-4 w-4 text-gray-400"}),c.jsxs("span",{className:"text-lg font-semibold text-gray-900",children:["$",a.currentPrice.toFixed(2)]})]})]}),c.jsxs("div",{className:"flex items-center space-x-2",children:[s(a.recommendation.action),c.jsx("span",{className:`badge ${l(a.recommendation.action)}`,children:a.recommendation.action})]})]}),c.jsxs("div",{className:"space-y-3",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Technical Indicators"}),c.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[c.jsxs("div",{className:"flex justify-between",children:[c.jsx("span",{className:"text-gray-600",children:"RSI:"}),c.jsx("span",{className:"font-medium",children:a.technicalIndicators.rsi.toFixed(1)})]}),c.jsxs("div",{className:"flex justify-between",children:[c.jsx("span",{className:"text-gray-600",children:"ATR:"}),c.jsx("span",{className:"font-medium",children:a.technicalIndicators.atr.toFixed(2)})]})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Sentiment"}),c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsx("span",{className:`badge ${o(a.recommendation.sentiment)}`,children:a.recommendation.sentiment}),c.jsxs("span",{className:"text-xs text-gray-500",children:[a.news.length," articles"]})]})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-1",children:"Confidence"}),c.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:c.jsx("div",{className:`h-2 rounded-full ${a.recommendation.confidence==="High"?"bg-success-500":a.recommendation.confidence==="Medium"?"bg-warning-500":"bg-gray-400"}`,style:{width:`${a.recommendation.confidence==="High"?100:a.recommendation.confidence==="Medium"?66:33}%`}})}),c.jsx("span",{className:"text-xs text-gray-500 mt-1 block",children:a.recommendation.confidence})]})]}),c.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:c.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:a.recommendation.reasoning})})]},a.ticker))})]})},f0=()=>{const{ticker:e}=yv(),{data:t,isLoading:n,error:r}=va(["ticker",e],()=>wa.getByTicker(e),{enabled:!!e,onError:()=>Sa("Failed to load ticker data","error")});if(n)return c.jsx("div",{className:"flex items-center justify-center h-64",children:c.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"})});if(r||!t)return c.jsxs("div",{className:"text-center py-12",children:[c.jsx("div",{className:"text-red-600 text-lg font-medium",children:"Failed to load ticker data"}),c.jsx("div",{className:"text-gray-600 mt-2",children:"Please try again later"}),c.jsx(Pn,{to:"/",className:"btn btn-primary mt-4 inline-block",children:"Back to Dashboard"})]});const i=a=>{switch(a){case"Buy":return c.jsx(is,{className:"h-6 w-6 text-success-600"});case"Sell":return c.jsx(co,{className:"h-6 w-6 text-danger-600"});case"Hold":return c.jsx(fr,{className:"h-6 w-6 text-warning-600"});default:return c.jsx(fr,{className:"h-6 w-6 text-gray-600"})}},s=a=>{switch(a){case"Buy":return"badge-success";case"Sell":return"badge-danger";case"Hold":return"badge-warning";default:return"badge-warning"}},l=a=>{switch(a){case"Positive":return"badge-success";case"Negative":return"badge-danger";case"Neutral":return"badge-warning";default:return"badge-warning"}},o=a=>{switch(a){case"bullish":return"badge-success";case"bearish":return"badge-danger";case"neutral":return"badge-warning";default:return"badge-warning"}};return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs(Pn,{to:"/",className:"btn btn-secondary",children:[c.jsx(ky,{className:"h-4 w-4 mr-2"}),"Back"]}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:t.ticker}),c.jsx("p",{className:"text-gray-600",children:"Detailed Analysis & Recommendations"})]})]}),c.jsxs("div",{className:"text-sm text-gray-500",children:["Last updated: ",new Date(t.lastUpdated).toLocaleString()]})]}),c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[c.jsx("div",{className:"card",children:c.jsx("div",{className:"flex items-center justify-between",children:c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Current Price"}),c.jsxs("div",{className:"flex items-center space-x-2 mt-2",children:[c.jsx(uo,{className:"h-5 w-5 text-gray-400"}),c.jsxs("span",{className:"text-3xl font-bold text-gray-900",children:["$",t.currentPrice.toFixed(2)]})]})]})})}),c.jsx("div",{className:"card",children:c.jsx("div",{className:"flex items-center justify-between",children:c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Recommendation"}),c.jsxs("div",{className:"flex items-center space-x-3 mt-2",children:[i(t.recommendation.action),c.jsx("span",{className:`badge text-sm ${s(t.recommendation.action)}`,children:t.recommendation.action})]})]})})}),c.jsx("div",{className:"card",children:c.jsxs("div",{children:[c.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"Confidence"}),c.jsxs("div",{className:"mt-2",children:[c.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:c.jsx("div",{className:`h-3 rounded-full ${t.recommendation.confidence==="High"?"bg-success-500":t.recommendation.confidence==="Medium"?"bg-warning-500":"bg-gray-400"}`,style:{width:`${t.recommendation.confidence==="High"?100:t.recommendation.confidence==="Medium"?66:33}%`}})}),c.jsxs("span",{className:"text-sm text-gray-600 mt-1 block",children:[t.recommendation.confidence," Confidence"]})]})]})})]}),c.jsxs("div",{className:"card",children:[c.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Technical Analysis"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"RSI (14)"}),c.jsx("div",{className:"text-2xl font-bold text-gray-900",children:t.technicalIndicators.rsi.toFixed(1)}),c.jsx("span",{className:`badge ${o(t.recommendation.technicalSignals.rsi)}`,children:t.recommendation.technicalSignals.rsi})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"MACD"}),c.jsxs("div",{className:"space-y-1",children:[c.jsxs("div",{className:"text-sm",children:[c.jsx("span",{className:"text-gray-600",children:"MACD:"}),c.jsx("span",{className:"font-medium ml-2",children:t.technicalIndicators.macd.macd.toFixed(3)})]}),c.jsxs("div",{className:"text-sm",children:[c.jsx("span",{className:"text-gray-600",children:"Signal:"}),c.jsx("span",{className:"font-medium ml-2",children:t.technicalIndicators.macd.signal.toFixed(3)})]})]}),c.jsx("span",{className:`badge ${o(t.recommendation.technicalSignals.macd)}`,children:t.recommendation.technicalSignals.macd})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Bollinger Bands"}),c.jsxs("div",{className:"space-y-1",children:[c.jsxs("div",{className:"text-sm",children:[c.jsx("span",{className:"text-gray-600",children:"Upper:"}),c.jsxs("span",{className:"font-medium ml-2",children:["$",t.technicalIndicators.bollingerBands.upper.toFixed(2)]})]}),c.jsxs("div",{className:"text-sm",children:[c.jsx("span",{className:"text-gray-600",children:"Lower:"}),c.jsxs("span",{className:"font-medium ml-2",children:["$",t.technicalIndicators.bollingerBands.lower.toFixed(2)]})]})]}),c.jsx("span",{className:`badge ${o(t.recommendation.technicalSignals.bollingerBands)}`,children:t.recommendation.technicalSignals.bollingerBands})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"SMA Crossover"}),c.jsxs("div",{className:"space-y-1",children:[c.jsxs("div",{className:"text-sm",children:[c.jsx("span",{className:"text-gray-600",children:"20-day:"}),c.jsxs("span",{className:"font-medium ml-2",children:["$",t.technicalIndicators.sma.short.toFixed(2)]})]}),c.jsxs("div",{className:"text-sm",children:[c.jsx("span",{className:"text-gray-600",children:"50-day:"}),c.jsxs("span",{className:"font-medium ml-2",children:["$",t.technicalIndicators.sma.long.toFixed(2)]})]})]}),c.jsx("span",{className:`badge ${o(t.recommendation.technicalSignals.sma)}`,children:t.recommendation.technicalSignals.sma})]})]})]}),c.jsxs("div",{className:"card",children:[c.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Risk Management"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(Cy,{className:"h-8 w-8 text-danger-500"}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700",children:"Stop Loss"}),c.jsxs("div",{className:"text-lg font-bold text-gray-900",children:["$",t.recommendation.riskManagement.stopLoss.toFixed(2)]})]})]}),c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(Oy,{className:"h-8 w-8 text-success-500"}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700",children:"Take Profit"}),c.jsxs("div",{className:"text-lg font-bold text-gray-900",children:["$",t.recommendation.riskManagement.takeProfit.toFixed(2)]})]})]}),c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(uo,{className:"h-8 w-8 text-primary-500"}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700",children:"Position Size"}),c.jsxs("div",{className:"text-lg font-bold text-gray-900",children:["$",t.recommendation.riskManagement.positionSize.toLocaleString()]})]})]}),c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsx(jy,{className:"h-8 w-8 text-warning-500"}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-sm font-medium text-gray-700",children:"Max Risk"}),c.jsxs("div",{className:"text-lg font-bold text-gray-900",children:[(t.recommendation.riskManagement.maxRisk*100).toFixed(1),"%"]})]})]})]})]}),c.jsxs("div",{className:"card",children:[c.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Sentiment Analysis"}),c.jsx("div",{className:"mb-4",children:c.jsxs("div",{className:"flex items-center space-x-3",children:[c.jsxs("span",{className:`badge ${l(t.recommendation.sentiment)}`,children:[t.recommendation.sentiment," Sentiment"]}),c.jsxs("span",{className:"text-sm text-gray-600",children:["Based on ",t.news.length," news articles"]})]})}),c.jsx("p",{className:"text-gray-700",children:t.recommendation.reasoning})]}),c.jsxs("div",{className:"card",children:[c.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-4",children:"Recent News"}),c.jsx("div",{className:"space-y-4",children:t.news.slice(0,5).map((a,u)=>c.jsx("div",{className:"border-b border-gray-200 pb-4 last:border-b-0",children:c.jsx("div",{className:"flex items-start justify-between",children:c.jsxs("div",{className:"flex-1",children:[c.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:c.jsx("a",{href:a.url,target:"_blank",rel:"noopener noreferrer",className:"hover:text-primary-600 transition-colors",children:a.title})}),c.jsxs("p",{className:"text-sm text-gray-600 mb-2",children:[a.content.substring(0,150),"..."]}),c.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[c.jsx("span",{children:a.source}),c.jsx("span",{children:new Date(a.publishedAt).toLocaleDateString()}),a.sentiment&&c.jsx("span",{className:`badge ${l(a.sentiment.sentiment)}`,children:a.sentiment.sentiment})]})]})})},u))})]})]})},d0=()=>{const[e,t]=j.useState({type:"",ticker:"",limit:50}),{data:n,isLoading:r,error:i,refetch:s}=va(["logs",e],()=>wa.getLogs(e),{refetchInterval:3e4,onError:()=>Sa("Failed to load logs","error")}),l=a=>{switch(a){case"technical_analysis":return"badge-primary";case"sentiment_analysis":return"badge-success";case"recommendation":return"badge-warning";case"error":return"badge-danger";default:return"badge-warning"}},o=a=>new Date(a).toLocaleString();return c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"System Logs"}),c.jsx("p",{className:"text-gray-600 mt-1",children:"Monitor system activity and recommendations"})]}),c.jsxs("button",{onClick:()=>s(),className:"btn btn-secondary",disabled:r,children:[c.jsx(Py,{className:`h-4 w-4 mr-2 ${r?"animate-spin":""}`}),"Refresh"]})]}),c.jsx("div",{className:"card",children:c.jsxs("div",{className:"flex items-center space-x-4",children:[c.jsxs("div",{className:"flex items-center space-x-2",children:[c.jsx(Ry,{className:"h-4 w-4 text-gray-500"}),c.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Filters:"})]}),c.jsxs("select",{value:e.type,onChange:a=>t(u=>({...u,type:a.target.value})),className:"px-3 py-1 border border-gray-300 rounded-md text-sm",children:[c.jsx("option",{value:"",children:"All Types"}),c.jsx("option",{value:"technical_analysis",children:"Technical Analysis"}),c.jsx("option",{value:"sentiment_analysis",children:"Sentiment Analysis"}),c.jsx("option",{value:"recommendation",children:"Recommendation"}),c.jsx("option",{value:"error",children:"Error"})]}),c.jsx("input",{type:"text",placeholder:"Filter by ticker...",value:e.ticker,onChange:a=>t(u=>({...u,ticker:a.target.value})),className:"px-3 py-1 border border-gray-300 rounded-md text-sm"}),c.jsxs("select",{value:e.limit,onChange:a=>t(u=>({...u,limit:parseInt(a.target.value)})),className:"px-3 py-1 border border-gray-300 rounded-md text-sm",children:[c.jsx("option",{value:25,children:"25 entries"}),c.jsx("option",{value:50,children:"50 entries"}),c.jsx("option",{value:100,children:"100 entries"})]})]})}),c.jsx("div",{className:"card",children:r?c.jsx("div",{className:"flex items-center justify-center h-32",children:c.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"})}):i?c.jsxs("div",{className:"text-center py-8",children:[c.jsx("div",{className:"text-red-600 text-lg font-medium",children:"Failed to load logs"}),c.jsx("div",{className:"text-gray-600 mt-2",children:"Please try again later"})]}):!n||n.length===0?c.jsxs("div",{className:"text-center py-8",children:[c.jsx(Bd,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),c.jsx("div",{className:"text-gray-600",children:"No logs found"})]}):c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[c.jsx("thead",{className:"bg-gray-50",children:c.jsxs("tr",{children:[c.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),c.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),c.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Ticker"}),c.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Message"})]})}),c.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:n.map(a=>c.jsxs("tr",{className:"hover:bg-gray-50",children:[c.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:o(a.timestamp)}),c.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:c.jsx("span",{className:`badge ${l(a.type)}`,children:a.type.replace("_"," ")})}),c.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.ticker||"-"}),c.jsx("td",{className:"px-6 py-4 text-sm text-gray-900",children:c.jsxs("div",{className:"max-w-md",children:[c.jsx("div",{className:"font-medium",children:a.message}),a.data&&c.jsxs("details",{className:"mt-2",children:[c.jsx("summary",{className:"text-xs text-gray-500 cursor-pointer hover:text-gray-700",children:"View details"}),c.jsx("pre",{className:"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto",children:JSON.stringify(a.data,null,2)})]})]})})]},a.id))})]})})}),n&&n.length>0&&c.jsxs("div",{className:"text-sm text-gray-500 text-center",children:["Showing ",n.length," log entries"]})]})};function h0(){return c.jsxs("div",{className:"min-h-screen bg-gray-50",children:[c.jsx(Ty,{}),c.jsx("main",{className:"container mx-auto px-4 py-8",children:c.jsxs(Lv,{children:[c.jsx(wi,{path:"/",element:c.jsx(c0,{})}),c.jsx(wi,{path:"/ticker/:ticker",element:c.jsx(f0,{})}),c.jsx(wi,{path:"/logs",element:c.jsx(d0,{})})]})}),c.jsx(u0,{})]})}const p0=new ay({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:1,staleTime:5*60*1e3}}});ul.createRoot(document.getElementById("root")).render(c.jsx(he.StrictMode,{children:c.jsx(py,{client:p0,children:c.jsx($v,{children:c.jsx(h0,{})})})}));
